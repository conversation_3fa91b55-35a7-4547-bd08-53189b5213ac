from PIL import Image, ImageFilter
import matplotlib.pyplot as plt

original_image = Image.open("C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/2/notre_dame.jpg")

img = original_image
img = img.filter(ImageFilter.BLUR)
for _ in range(5):    
    img = img.filter(ImageFilter.SHARPEN)
processed_image = img

plt.imshow(processed_image)
plt.axis('off')
plt.show()

processed_image.save("C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/2/2B.jpg")

