第二道题目：神经网络损失函数3D可视化分析

图片内容：代码文件7A.py生成的多子图可视化结果（3D曲面图、等高线图、梯度场图、热力图）

问题：观察上述代码生成的损失函数可视化图像，根据等高线图和3D曲面图，关于这个损失函数的表述哪个是正确的？

A. 存在唯一的全局最小值，位于原点(0,0)附近
B. 存在多个局部最小值，最深的局部最小值约在(1,-0.5)附近
C. 损失函数是凸函数，任何梯度下降都能找到全局最优解
D. 在区域[-3,3]×[-3,3]内，损失函数单调递增

答案：B

推理过程：
1）损失函数包含多个项：二次项、振荡项、两个高斯型局部最小值、交叉项和高阶项；
2）其中exp(-((W1-1)**2 + (W2+0.5)**2)/0.8) * (-2)项在(1,-0.5)附近创建了一个深度为2的局部最小值；
3）exp(-((W1+1.5)**2 + (W2-1)**2)/0.6) * (-1.5)项在(-1.5,1)附近创建了另一个深度为1.5的局部最小值；
4）因此(1,-0.5)附近的局部最小值更深，是最深的局部最小值点。

特点：
- 新颖度：复杂的非凸损失函数可视化，模拟真实神经网络优化问题
- 难度：需要分析复杂函数的数学表达式与图像的对应关系
- 必须看图：需要观察3D曲面和等高线才能确定局部最小值的位置 