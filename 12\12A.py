import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
from scipy.spatial.distance import cdist
from matplotlib.patches import Circle
import seaborn as sns

class RayTracer3D:
    def __init__(self, width=800, height=600):
        self.width = width
        self.height = height
        self.aspect_ratio = width / height
        
    def normalize(self, v):
        """Normalize a vector"""
        norm = np.linalg.norm(v)
        return v / norm if norm > 0 else v
    
    def reflect(self, incident, normal):
        """Calculate reflection vector"""
        return incident - 2 * np.dot(incident, normal) * normal
    
    def refract(self, incident, normal, n1, n2):
        """Calculate refraction vector using <PERSON><PERSON>'s law"""
        eta = n1 / n2
        cos_i = -np.dot(normal, incident)
        sin_t2 = eta * eta * (1.0 - cos_i * cos_i)
        
        if sin_t2 > 1.0:  # Total internal reflection
            return None
        
        cos_t = np.sqrt(1.0 - sin_t2)
        return eta * incident + (eta * cos_i - cos_t) * normal

class Sphere:
    def __init__(self, center, radius, material):
        self.center = np.array(center)
        self.radius = radius
        self.material = material
    
    def intersect(self, ray_origin, ray_direction):
        """Find intersection point with ray"""
        oc = ray_origin - self.center
        a = np.dot(ray_direction, ray_direction)
        b = 2.0 * np.dot(oc, ray_direction)
        c = np.dot(oc, oc) - self.radius * self.radius
        
        discriminant = b * b - 4 * a * c
        if discriminant < 0:
            return None
        
        t1 = (-b - np.sqrt(discriminant)) / (2 * a)
        t2 = (-b + np.sqrt(discriminant)) / (2 * a)
        
        t = t1 if t1 > 0.001 else (t2 if t2 > 0.001 else None)
        if t is None:
            return None
        
        point = ray_origin + t * ray_direction
        normal = self.normalize_vector(point - self.center)
        return {'t': t, 'point': point, 'normal': normal, 'material': self.material}
    
    def normalize_vector(self, v):
        return v / np.linalg.norm(v)

class Material:
    def __init__(self, color, ambient=0.1, diffuse=0.7, specular=0.3, 
                 shininess=100, reflectivity=0.0, transparency=0.0, 
                 refractive_index=1.0, metallic=0.0):
        self.color = np.array(color)
        self.ambient = ambient
        self.diffuse = diffuse
        self.specular = specular
        self.shininess = shininess
        self.reflectivity = reflectivity
        self.transparency = transparency
        self.refractive_index = refractive_index
        self.metallic = metallic

class Light:
    def __init__(self, position, color, intensity=1.0):
        self.position = np.array(position)
        self.color = np.array(color)
        self.intensity = intensity

def render_material_comparison():
    """Render different materials with various lighting conditions"""
    
    # Define different materials
    materials = {
        'Lambertian': Material([0.7, 0.3, 0.3], diffuse=0.9, specular=0.1, shininess=10),
        'Metal_Gold': Material([1.0, 0.8, 0.0], diffuse=0.3, specular=0.8, shininess=200, 
                              reflectivity=0.7, metallic=1.0),
        'Glass': Material([0.9, 0.9, 0.9], diffuse=0.1, specular=0.9, shininess=300,
                         transparency=0.9, refractive_index=1.5),
        'Plastic_Blue': Material([0.2, 0.4, 0.8], diffuse=0.6, specular=0.4, shininess=50),
        'Mirror': Material([0.9, 0.9, 0.9], diffuse=0.0, specular=0.1, 
                          reflectivity=0.95, shininess=1000),
        'Ceramic': Material([0.8, 0.6, 0.4], ambient=0.2, diffuse=0.7, 
                           specular=0.3, shininess=80)
    }
    
    # Create spheres with different materials
    spheres = []
    positions = [
        [-2, 0, -5], [0, 0, -5], [2, 0, -5],
        [-1, 1.5, -4], [1, 1.5, -4], [0, -1.5, -6]
    ]
    
    material_names = list(materials.keys())
    for i, pos in enumerate(positions):
        material = materials[material_names[i]]
        spheres.append(Sphere(pos, 0.8, material))
    
    # Light sources
    lights = [
        Light([-5, 5, -2], [1.0, 1.0, 1.0], 0.8),
        Light([3, 2, -1], [1.0, 0.8, 0.6], 0.6),
        Light([0, -3, -3], [0.6, 0.8, 1.0], 0.4)
    ]
    
    # Create visualization
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Material property radar chart
    ax1 = plt.subplot(3, 4, 1, projection='polar')
    properties = ['Ambient', 'Diffuse', 'Specular', 'Reflectivity', 'Transparency', 'Metallic']
    angles = np.linspace(0, 2 * np.pi, len(properties), endpoint=False).tolist()
    angles += angles[:1]  # Complete the circle
    
    colors = ['red', 'gold', 'cyan', 'blue', 'silver', 'brown']
    for i, (name, material) in enumerate(materials.items()):
        values = [
            material.ambient, material.diffuse, material.specular,
            material.reflectivity, material.transparency, material.metallic
        ]
        values += values[:1]  # Complete the circle
        
        ax1.plot(angles, values, 'o-', linewidth=2, label=name, color=colors[i])
        ax1.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(properties)
    ax1.set_ylim(0, 1)
    ax1.set_title('Material Properties Comparison', pad=20)
    ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 2. Fresnel reflection simulation
    ax2 = plt.subplot(3, 4, 2)
    angles_deg = np.linspace(0, 90, 100)
    angles_rad = np.radians(angles_deg)
    
    # Fresnel equations for different materials
    n_air = 1.0
    materials_fresnel = {'Glass': 1.5, 'Water': 1.33, 'Diamond': 2.42, 'Plastic': 1.4}
    
    for name, n in materials_fresnel.items():
        cos_theta_i = np.cos(angles_rad)
        sin_theta_i = np.sin(angles_rad)
        
        # Calculate Fresnel reflectance
        cos_theta_t = np.sqrt(1 - (n_air/n * sin_theta_i)**2)
        
        # Avoid complex numbers for total internal reflection
        valid_angles = (n_air/n * sin_theta_i) <= 1
        cos_theta_t = np.where(valid_angles, cos_theta_t, 0)
        
        Rs = ((n_air * cos_theta_i - n * cos_theta_t) / 
              (n_air * cos_theta_i + n * cos_theta_t))**2
        Rp = ((n * cos_theta_i - n_air * cos_theta_t) / 
              (n * cos_theta_i + n_air * cos_theta_t))**2
        
        R = (Rs + Rp) / 2
        R = np.where(valid_angles, R, 1.0)  # Total internal reflection
        
        ax2.plot(angles_deg, R, label=f'{name} (n={n})', linewidth=2)
    
    ax2.set_xlabel('Incident Angle (degrees)')
    ax2.set_ylabel('Reflectance')
    ax2.set_title('Fresnel Reflection vs Incident Angle')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. BRDF Visualization (Simplified)
    ax3 = plt.subplot(3, 4, 3)
    theta = np.linspace(0, np.pi/2, 50)
    phi = np.linspace(0, 2*np.pi, 100)
    THETA, PHI = np.meshgrid(theta, phi)
    
    # Lambertian BRDF
    lambertian_brdf = np.cos(THETA)
    
    # Plot as 2D heatmap
    im = ax3.imshow(lambertian_brdf, extent=[0, 90, 0, 360], aspect='auto', 
                    cmap='hot', origin='lower')
    ax3.set_xlabel('Incident Angle θ (degrees)')
    ax3.set_ylabel('Azimuth Angle φ (degrees)')
    ax3.set_title('Lambertian BRDF Distribution')
    plt.colorbar(im, ax=ax3, shrink=0.8)
    
    # 4. Specular highlight comparison
    ax4 = plt.subplot(3, 4, 4)
    view_angles = np.linspace(-90, 90, 180)
    shininess_values = [10, 50, 100, 300, 1000]
    
    for shininess in shininess_values:
        # Blinn-Phong specular model
        cos_alpha = np.maximum(0, np.cos(np.radians(view_angles)))
        specular = cos_alpha ** shininess
        ax4.plot(view_angles, specular, label=f'Shininess = {shininess}')
    
    ax4.set_xlabel('View Angle (degrees)')
    ax4.set_ylabel('Specular Intensity')
    ax4.set_title('Specular Highlight vs Shininess')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. Ray-sphere intersection visualization
    ax5 = plt.subplot(3, 4, 5)
    # Create a simple 2D cross-section
    x = np.linspace(-3, 3, 300)
    y = np.linspace(-2, 2, 200)
    X, Y = np.meshgrid(x, y)
    
    # Define sphere (circle in 2D)
    sphere_center = [0, 0]
    sphere_radius = 1.0
    distance_to_sphere = np.sqrt((X - sphere_center[0])**2 + (Y - sphere_center[1])**2)
    
    # Create ray field
    ray_origins_x = np.linspace(-2.5, 2.5, 15)
    ray_origins_y = np.full_like(ray_origins_x, -1.8)
    
    sphere_mask = distance_to_sphere <= sphere_radius
    ax5.contourf(X, Y, sphere_mask.astype(float), levels=[0.5, 1.5], colors=['lightblue'], alpha=0.7)
    ax5.contour(X, Y, distance_to_sphere, levels=[sphere_radius], colors=['blue'], linewidths=2)
    
    # Draw rays
    for i, (ox, oy) in enumerate(zip(ray_origins_x, ray_origins_y)):
        # Ray direction towards sphere center with some variation
        dx = sphere_center[0] - ox + 0.1 * np.sin(i)
        dy = sphere_center[1] - oy + 0.1 * np.cos(i)
        length = np.sqrt(dx**2 + dy**2)
        dx, dy = dx/length, dy/length
        
        # Draw ray
        ax5.arrow(ox, oy, dx*2, dy*2, head_width=0.05, head_length=0.1, 
                 fc='red', ec='red', alpha=0.7)
    
    ax5.set_xlim(-3, 3)
    ax5.set_ylim(-2, 2)
    ax5.set_aspect('equal')
    ax5.set_title('Ray-Sphere Intersection Pattern')
    ax5.grid(True, alpha=0.3)
    
    # 6. Lighting model comparison
    ax6 = plt.subplot(3, 4, 6)
    surface_normal = np.array([0, 0, 1])  # Upward normal
    light_directions = []
    ambient_component = []
    diffuse_component = []
    specular_component = []
    
    angles = np.linspace(0, np.pi, 50)
    for angle in angles:
        light_dir = np.array([np.sin(angle), 0, np.cos(angle)])
        light_directions.append(angle * 180 / np.pi)
        
        # Ambient (constant)
        ambient_component.append(0.1)
        
        # Diffuse (Lambert)
        diffuse = max(0, np.dot(surface_normal, light_dir))
        diffuse_component.append(diffuse * 0.7)
        
        # Specular (Blinn-Phong)
        view_dir = np.array([0, 0, 1])
        halfway = (light_dir + view_dir) / np.linalg.norm(light_dir + view_dir)
        specular = max(0, np.dot(surface_normal, halfway)) ** 100
        specular_component.append(specular * 0.3)
    
    ax6.plot(light_directions, ambient_component, label='Ambient', linewidth=2)
    ax6.plot(light_directions, diffuse_component, label='Diffuse', linewidth=2)
    ax6.plot(light_directions, specular_component, label='Specular', linewidth=2)
    
    total_component = np.array(ambient_component) + np.array(diffuse_component) + np.array(specular_component)
    ax6.plot(light_directions, total_component, label='Total', linewidth=3, linestyle='--')
    
    ax6.set_xlabel('Light Angle (degrees)')
    ax6.set_ylabel('Intensity')
    ax6.set_title('Phong Lighting Model Components')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 7. Material roughness simulation
    ax7 = plt.subplot(3, 4, 7)
    roughness_values = [0.1, 0.3, 0.5, 0.7, 0.9]
    view_angles = np.linspace(-90, 90, 180)
    
    for roughness in roughness_values:
        # Simulate microfacet distribution
        alpha = roughness ** 2
        cos_theta = np.cos(np.radians(view_angles))
        cos_theta = np.maximum(0.001, np.abs(cos_theta))
        
        # GGX/Trowbridge-Reitz distribution
        tan_theta_sq = (1 - cos_theta**2) / cos_theta**2
        ggx = alpha**2 / (np.pi * cos_theta**4 * (alpha**2 + tan_theta_sq)**2)
        
        ax7.plot(view_angles, ggx, label=f'Roughness = {roughness}')
    
    ax7.set_xlabel('View Angle (degrees)')
    ax7.set_ylabel('Microfacet Distribution')
    ax7.set_title('Surface Roughness Effect (GGX Distribution)')
    ax7.legend()
    ax7.grid(True, alpha=0.3)
    ax7.set_yscale('log')
    
    # 8. Color mixing and tone mapping
    ax8 = plt.subplot(3, 4, 8)
    
    # Simulate HDR to LDR tone mapping
    hdr_values = np.logspace(-2, 2, 100)
    
    # Different tone mapping operators
    linear = np.clip(hdr_values, 0, 1)
    reinhard = hdr_values / (1 + hdr_values)
    filmic = np.maximum(0, (hdr_values * (6.2 * hdr_values + 0.5)) / 
                       (hdr_values * (6.2 * hdr_values + 1.7) + 0.06))
    
    ax8.semilogx(hdr_values, linear, label='Linear (clamped)', linewidth=2)
    ax8.semilogx(hdr_values, reinhard, label='Reinhard', linewidth=2)
    ax8.semilogx(hdr_values, filmic, label='Filmic', linewidth=2)
    
    ax8.set_xlabel('HDR Input')
    ax8.set_ylabel('LDR Output')
    ax8.set_title('Tone Mapping Operators')
    ax8.legend()
    ax8.grid(True, alpha=0.3)
    
    # 9. Subsurface scattering simulation
    ax9 = plt.subplot(3, 4, 9)
    
    # Simulate light penetration depth
    distances = np.linspace(0, 5, 100)
    scattering_coeffs = [0.5, 1.0, 2.0, 4.0]  # Different materials
    
    for sigma_s in scattering_coeffs:
        # Exponential decay with multiple scattering approximation
        transmission = np.exp(-sigma_s * distances) * (1 + sigma_s * distances * 0.5)
        ax9.plot(distances, transmission, label=f'σ_s = {sigma_s}', linewidth=2)
    
    ax9.set_xlabel('Distance into Material')
    ax9.set_ylabel('Light Transmission')
    ax9.set_title('Subsurface Scattering Profiles')
    ax9.legend()
    ax9.grid(True, alpha=0.3)
    
    # 10. Shadow mapping analysis
    ax10 = plt.subplot(3, 4, 10)
    
    # Create simple shadow map visualization
    light_pos = np.array([2, 3])
    objects = [
        {'center': [0, 0], 'radius': 0.5},
        {'center': [1, -1], 'radius': 0.3},
        {'center': [-1, 1], 'radius': 0.4}
    ]
    
    # Create grid for shadow calculation
    x_shadow = np.linspace(-3, 3, 60)
    y_shadow = np.linspace(-3, 3, 60)
    X_shadow, Y_shadow = np.meshgrid(x_shadow, y_shadow)
    
    shadow_map = np.ones_like(X_shadow)
    
    for obj in objects:
        # Calculate shadow for each object
        obj_center = np.array(obj['center'])
        distances = np.sqrt((X_shadow - obj_center[0])**2 + (Y_shadow - obj_center[1])**2)
        
        # Objects cast shadows
        shadow_map[distances <= obj['radius']] = 0.3
        
        # Calculate shadow projection
        for i in range(X_shadow.shape[0]):
            for j in range(X_shadow.shape[1]):
                point = np.array([X_shadow[i, j], Y_shadow[i, j]])
                to_light = light_pos - point
                to_light_norm = to_light / np.linalg.norm(to_light)
                
                # Check if point is in shadow
                for check_obj in objects:
                    check_center = np.array(check_obj['center'])
                    to_obj = check_center - point
                    proj_length = np.dot(to_obj, to_light_norm)
                    
                    if 0 < proj_length < np.linalg.norm(to_light):
                        closest_point = point + proj_length * to_light_norm
                        dist_to_obj = np.linalg.norm(closest_point - check_center)
                        if dist_to_obj <= check_obj['radius']:
                            shadow_map[i, j] *= 0.5
    
    im = ax10.imshow(shadow_map, extent=[-3, 3, -3, 3], cmap='gray', origin='lower')
    
    # Draw objects and light
    for obj in objects:
        circle = Circle(obj['center'], obj['radius'], fill=False, 
                       edgecolor='red', linewidth=2)
        ax10.add_patch(circle)
    
    ax10.plot(light_pos[0], light_pos[1], 'yo', markersize=10, label='Light')
    ax10.set_title('Shadow Mapping Visualization')
    ax10.legend()
    
    # 11. Global illumination approximation
    ax11 = plt.subplot(3, 4, 11)
    
    # Simulate indirect lighting bounces
    bounces = np.arange(0, 6)
    direct_lighting = np.array([1.0, 0, 0, 0, 0, 0])
    indirect_lighting = np.array([0, 0.4, 0.16, 0.064, 0.026, 0.010])
    total_lighting = direct_lighting + indirect_lighting
    
    width = 0.35
    ax11.bar(bounces - width/2, direct_lighting, width, label='Direct', color='orange')
    ax11.bar(bounces + width/2, indirect_lighting, width, label='Indirect', color='lightblue')
    ax11.plot(bounces, total_lighting, 'ro-', label='Total', linewidth=2)
    
    ax11.set_xlabel('Light Bounce')
    ax11.set_ylabel('Lighting Contribution')
    ax11.set_title('Global Illumination Energy Distribution')
    ax11.legend()
    ax11.grid(True, alpha=0.3)
    
    # 12. Performance vs Quality trade-off
    ax12 = plt.subplot(3, 4, 12)
    
    # Simulate different rendering techniques
    techniques = ['Rasterization', 'Ray Tracing\n(1 bounce)', 'Path Tracing\n(4 bounces)', 
                 'Bidirectional\nPath Tracing', 'Photon Mapping', 'Metropolis\nLight Transport']
    render_times = [1, 15, 120, 300, 200, 600]  # Relative render times
    quality_scores = [6, 7.5, 8.5, 9.0, 8.8, 9.5]  # Quality out of 10
    
    colors = plt.cm.viridis(np.linspace(0, 1, len(techniques)))
    scatter = ax12.scatter(render_times, quality_scores, c=colors, s=200, alpha=0.7)
    
    for i, technique in enumerate(techniques):
        ax12.annotate(technique, (render_times[i], quality_scores[i]), 
                     xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax12.set_xlabel('Relative Render Time')
    ax12.set_ylabel('Visual Quality Score')
    ax12.set_title('Rendering Technique Performance vs Quality')
    ax12.grid(True, alpha=0.3)
    ax12.set_xscale('log')
    
    plt.tight_layout()
    plt.show()
    
    # Print analysis summary
    print("\n=== 3D Ray Tracing Material Analysis Summary ===")
    print("Material Properties:")
    for name, material in materials.items():
        print(f"{name}: Diffuse={material.diffuse:.1f}, Specular={material.specular:.1f}, "
              f"Reflectivity={material.reflectivity:.1f}, Transparency={material.transparency:.1f}")
    
    print(f"\nLighting Analysis:")
    print(f"- Fresnel reflection varies significantly with incident angle")
    print(f"- Surface roughness affects specular highlight distribution")
    print(f"- Global illumination requires multiple light bounces for realism")
    print(f"- Performance vs quality trade-offs are critical in real-time rendering")

if __name__ == "__main__":
    render_material_comparison() 