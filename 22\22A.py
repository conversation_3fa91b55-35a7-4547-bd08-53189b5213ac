import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np
import math
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
import os

# 设置matplotlib后端和中文字体
plt.switch_backend('Agg')  # 使用非交互式后端
plt.rcParams['font.family'] = ['DejaVu Sans', 'SimHei']  # 备用字体
plt.rcParams['axes.unicode_minus'] = False

# 确保输出目录存在
os.makedirs('22', exist_ok=True)

class BuggyMultiModalAttention(nn.Module):
    """
    有错误的多模态注意力机制实现
    错误：缺少缩放因子，导致数值不稳定
    """
    def __init__(self, d_model=512, num_heads=8):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 文本和图像的查询、键、值投影
        self.text_query = nn.Linear(d_model, d_model)
        self.text_key = nn.Linear(d_model, d_model)
        self.text_value = nn.Linear(d_model, d_model)
        
        self.image_query = nn.Linear(d_model, d_model)
        self.image_key = nn.Linear(d_model, d_model)
        self.image_value = nn.Linear(d_model, d_model)
        
        self.output_proj = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, text_features, image_features):
        batch_size, seq_len, _ = text_features.shape
        
        # 计算查询、键、值
        text_q = self.text_query(text_features).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        text_k = self.text_key(text_features).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        text_v = self.text_value(text_features).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        image_q = self.image_query(image_features).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        image_k = self.image_key(image_features).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        image_v = self.image_value(image_features).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # 错误的注意力计算：缺少缩放因子！
        text_scores = torch.matmul(text_q, text_k.transpose(-2, -1))  # 应该除以sqrt(d_k)
        image_scores = torch.matmul(image_q, image_k.transpose(-2, -1))  # 应该除以sqrt(d_k)
        cross_scores = torch.matmul(text_q, image_k.transpose(-2, -1))  # 应该除以sqrt(d_k)
        
        # 由于缺少缩放，softmax会产生极端值
        text_attention = F.softmax(text_scores, dim=-1)
        image_attention = F.softmax(image_scores, dim=-1)
        cross_attention = F.softmax(cross_scores, dim=-1)
        
        return text_attention, image_attention, cross_attention

def generate_sample_features():
    """生成示例特征，模拟真实的文本和图像特征"""
    torch.manual_seed(42)
    batch_size, seq_len, d_model = 1, 16, 512
    
    # 生成具有不同模式的特征
    text_features = torch.randn(batch_size, seq_len, d_model) * 2.0  # 较大的方差
    image_features = torch.randn(batch_size, seq_len, d_model) * 1.5
    
    # 添加一些结构化模式
    for i in range(seq_len):
        text_features[0, i, :] += torch.sin(torch.arange(d_model).float() * i * 0.1)
        image_features[0, i, :] += torch.cos(torch.arange(d_model).float() * i * 0.1)
    
    return text_features, image_features

def create_code_visualization():
    """创建代码错误的可视化"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左侧：错误代码展示
    ax1.text(0.05, 0.95, "多模态注意力机制实现", 
             transform=ax1.transAxes, fontsize=14, fontweight='bold', va='top')
    
    code_text = """class BuggyMultiModalAttention(nn.Module):
    def __init__(self, d_model=512, num_heads=8):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.text_query = nn.Linear(d_model, d_model)
        self.text_key = nn.Linear(d_model, d_model)
        
    def forward(self, text_features, image_features):
        text_q = self.text_query(text_features)
        text_k = self.text_key(text_features)
        
        text_scores = torch.matmul(text_q, text_k.transpose(-2, -1))
        cross_scores = torch.matmul(text_q, image_k.transpose(-2, -1))
    
        attention_weights = F.softmax(text_scores, dim=-1)
        
        return attention_weights"""
    
    ax1.text(0.05, 0.85, code_text, transform=ax1.transAxes, fontsize=9, 
             va='top', ha='left', family='monospace',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # 右侧：异常注意力热力图
    model = BuggyMultiModalAttention()
    text_features, image_features = generate_sample_features()
    
    with torch.no_grad():
        text_attention, image_attention, cross_attention = model(text_features, image_features)
    
    # 取第一个头的注意力权重进行可视化
    attention_matrix = cross_attention[0, 0].numpy()  # [seq_len, seq_len]
    
    im = ax2.imshow(attention_matrix, cmap='RdBu_r', aspect='auto')
    ax2.set_title('注意力权重热力图\n', fontsize=14, fontweight='bold')
    ax2.set_xlabel('图像特征位置')
    ax2.set_ylabel('文本特征位置')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax2)
    cbar.set_label('注意力权重', rotation=270, labelpad=20)
    
    # 标注异常值
    max_val = np.max(attention_matrix)
    min_val = np.min(attention_matrix)
    
    plt.tight_layout()
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/22/multimodal_attention_debug.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存

def create_solution_comparison():
    """创建正确解决方案的对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 四个选项的代码片段和效果
    options = [
        ("A. Temperature Scaling", "attention_weights = F.softmax(scores / temperature, dim=-1)", False),
        ("B. Scaled Dot-Product (正确)", "scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_k)", True),
        ("C. LayerNorm", "output = self.layer_norm(torch.cat(multi_head_outputs, dim=-1))", False),
        ("D. Dropout + Residual", "output = self.dropout(attention_output) + residual", False)
    ]
    
    axes = [ax1, ax2, ax3, ax4]
    
    for i, (title, code, is_correct) in enumerate(options):
        ax = axes[i]
        
        # 模拟不同修复方案的效果
        if is_correct:
            # 正确的缩放注意力
            torch.manual_seed(42)
            d_k = 64
            scores = torch.randn(16, 16) * 2.0
            scaled_scores = scores / math.sqrt(d_k)
            attention = F.softmax(scaled_scores, dim=-1).numpy()
            color = 'Greens'
            border_color = 'green'
        else:
            # 其他方案仍有问题
            torch.manual_seed(42)
            scores = torch.randn(16, 16) * 2.0
            if i == 0:  # Temperature scaling但仍无缩放
                attention = F.softmax(scores / 2.0, dim=-1).numpy()
            else:
                attention = F.softmax(scores, dim=-1).numpy()
            color = 'Reds'
            border_color = 'red'
        
        im = ax.imshow(attention, cmap=color, aspect='auto')
        ax.set_title(title, fontsize=12, fontweight='bold')
        
        # 添加边框表示正确性
        for spine in ax.spines.values():
            spine.set_edgecolor(border_color)
            spine.set_linewidth(3)
        
        # 添加代码片段
        ax.text(0.5, -0.15, code, transform=ax.transAxes, fontsize=9, 
                ha='center', va='top', family='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
        
        plt.colorbar(im, ax=ax, shrink=0.8)
    
    plt.suptitle('Different Solution Comparison', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/22/solution_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存

if __name__ == "__main__":
    print("Generating multimodal attention debugging question...")
    try:
        create_code_visualization()
        print("Main visualization created successfully!")
        create_solution_comparison()
        print("Solution comparison created successfully!")
        print("All images generated successfully!")
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
