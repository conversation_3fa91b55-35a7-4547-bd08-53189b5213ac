import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.stats import norm
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from scipy.optimize import minimize
import pandas as pd

class OptionPricingModel:
    def __init__(self):
        self.risk_free_rate = 0.05  # 5% risk-free rate
        
    def black_scholes_call(self, S, K, T, r, sigma):
        """Black-Scholes formula for European call option"""
        if T <= 0:
            return max(S - K, 0)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        call_price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        return call_price
    
    def black_scholes_put(self, S, K, T, r, sigma):
        """Black-Scholes formula for European put option"""
        if T <= 0:
            return max(K - S, 0)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        put_price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        return put_price
    
    def calculate_greeks(self, S, K, T, r, sigma, option_type='call'):
        """Calculate all Greeks for an option"""
        if T <= 0:
            return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0, 'rho': 0}
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        # Delta
        if option_type == 'call':
            delta = norm.cdf(d1)
        else:
            delta = norm.cdf(d1) - 1
        
        # Gamma (same for calls and puts)
        gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))
        
        # Theta
        if option_type == 'call':
            theta = (-S * norm.pdf(d1) * sigma / (2 * np.sqrt(T)) 
                    - r * K * np.exp(-r * T) * norm.cdf(d2)) / 365
        else:
            theta = (-S * norm.pdf(d1) * sigma / (2 * np.sqrt(T)) 
                    + r * K * np.exp(-r * T) * norm.cdf(-d2)) / 365
        
        # Vega (same for calls and puts)
        vega = S * norm.pdf(d1) * np.sqrt(T) / 100
        
        # Rho
        if option_type == 'call':
            rho = K * T * np.exp(-r * T) * norm.cdf(d2) / 100
        else:
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
        
        return {'delta': delta, 'gamma': gamma, 'theta': theta, 'vega': vega, 'rho': rho}
    
    def monte_carlo_option_price(self, S, K, T, r, sigma, n_simulations=100000, option_type='call'):
        """Monte Carlo simulation for option pricing"""
        np.random.seed(42)
        
        # Generate random paths
        Z = np.random.standard_normal(n_simulations)
        ST = S * np.exp((r - 0.5 * sigma**2) * T + sigma * np.sqrt(T) * Z)
        
        if option_type == 'call':
            payoffs = np.maximum(ST - K, 0)
        else:
            payoffs = np.maximum(K - ST, 0)
        
        option_price = np.exp(-r * T) * np.mean(payoffs)
        return option_price, ST, payoffs
    
    def binomial_tree_american(self, S, K, T, r, sigma, n_steps=100, option_type='call'):
        """Binomial tree model for American options"""
        dt = T / n_steps
        u = np.exp(sigma * np.sqrt(dt))
        d = 1 / u
        p = (np.exp(r * dt) - d) / (u - d)
        
        # Initialize asset prices at maturity
        asset_prices = np.zeros(n_steps + 1)
        for i in range(n_steps + 1):
            asset_prices[i] = S * (u ** (n_steps - i)) * (d ** i)
        
        # Initialize option values at maturity
        option_values = np.zeros(n_steps + 1)
        for i in range(n_steps + 1):
            if option_type == 'call':
                option_values[i] = max(asset_prices[i] - K, 0)
            else:
                option_values[i] = max(K - asset_prices[i], 0)
        
        # Backward induction
        for j in range(n_steps - 1, -1, -1):
            for i in range(j + 1):
                # European value
                european_value = np.exp(-r * dt) * (p * option_values[i] + (1 - p) * option_values[i + 1])
                
                # Current asset price
                current_asset_price = S * (u ** (j - i)) * (d ** i)
                
                # Intrinsic value
                if option_type == 'call':
                    intrinsic_value = max(current_asset_price - K, 0)
                else:
                    intrinsic_value = max(K - current_asset_price, 0)
                
                # American option value (max of European and intrinsic)
                option_values[i] = max(european_value, intrinsic_value)
        
        return option_values[0]

def comprehensive_option_analysis():
    """Comprehensive analysis of option pricing models and Greeks"""
    
    model = OptionPricingModel()
    
    # Base parameters
    S0 = 100  # Current stock price
    K = 100   # Strike price
    T = 0.25  # Time to expiration (3 months)
    r = 0.05  # Risk-free rate
    sigma = 0.2  # Volatility
    
    # Create comprehensive visualization
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Option price vs stock price (payoff diagram)
    ax1 = plt.subplot(3, 4, 1)
    
    stock_prices = np.linspace(70, 130, 100)
    call_prices = [model.black_scholes_call(S, K, T, r, sigma) for S in stock_prices]
    put_prices = [model.black_scholes_put(S, K, T, r, sigma) for S in stock_prices]
    call_intrinsic = np.maximum(stock_prices - K, 0)
    put_intrinsic = np.maximum(K - stock_prices, 0)
    
    ax1.plot(stock_prices, call_prices, 'b-', label='Call Option', linewidth=2)
    ax1.plot(stock_prices, put_prices, 'r-', label='Put Option', linewidth=2)
    ax1.plot(stock_prices, call_intrinsic, 'b--', label='Call Intrinsic', alpha=0.5)
    ax1.plot(stock_prices, put_intrinsic, 'r--', label='Put Intrinsic', alpha=0.5)
    ax1.axvline(K, color='black', linestyle=':', alpha=0.7, label='Strike')
    ax1.axvline(S0, color='green', linestyle=':', alpha=0.7, label='Current Price')
    
    ax1.set_title('Option Price vs Stock Price')
    ax1.set_xlabel('Stock Price')
    ax1.set_ylabel('Option Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Greeks vs Stock Price
    ax2 = plt.subplot(3, 4, 2)
    
    deltas_call = []
    deltas_put = []
    gammas = []
    
    for S in stock_prices:
        greeks_call = model.calculate_greeks(S, K, T, r, sigma, 'call')
        greeks_put = model.calculate_greeks(S, K, T, r, sigma, 'put')
        
        deltas_call.append(greeks_call['delta'])
        deltas_put.append(greeks_put['delta'])
        gammas.append(greeks_call['gamma'])
    
    ax2_twin = ax2.twinx()
    
    line1 = ax2.plot(stock_prices, deltas_call, 'b-', label='Call Delta', linewidth=2)
    line2 = ax2.plot(stock_prices, deltas_put, 'r-', label='Put Delta', linewidth=2)
    line3 = ax2_twin.plot(stock_prices, gammas, 'g-', label='Gamma', linewidth=2)
    
    ax2.set_title('Delta and Gamma vs Stock Price')
    ax2.set_xlabel('Stock Price')
    ax2.set_ylabel('Delta', color='black')
    ax2_twin.set_ylabel('Gamma', color='green')
    ax2.axvline(K, color='black', linestyle=':', alpha=0.7)
    ax2.axvline(S0, color='orange', linestyle=':', alpha=0.7)
    
    # Combine legends
    lines = line1 + line2 + line3
    labels = [l.get_label() for l in lines]
    ax2.legend(lines, labels, loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    # 3. Theta and Vega vs Time to Expiration
    ax3 = plt.subplot(3, 4, 3)
    
    times = np.linspace(0.01, 1, 50)  # From 1 day to 1 year
    thetas_call = []
    vegas_call = []
    
    for t in times:
        greeks = model.calculate_greeks(S0, K, t, r, sigma, 'call')
        thetas_call.append(greeks['theta'])
        vegas_call.append(greeks['vega'])
    
    ax3_twin = ax3.twinx()
    
    line1 = ax3.plot(times * 365, thetas_call, 'purple', label='Theta', linewidth=2)
    line2 = ax3_twin.plot(times * 365, vegas_call, 'orange', label='Vega', linewidth=2)
    
    ax3.set_title('Theta and Vega vs Time to Expiration')
    ax3.set_xlabel('Days to Expiration')
    ax3.set_ylabel('Theta (per day)', color='purple')
    ax3_twin.set_ylabel('Vega (per 1% vol)', color='orange')
    
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax3.legend(lines, labels, loc='upper right')
    ax3.grid(True, alpha=0.3)
    
    # 4. Volatility Smile
    ax4 = plt.subplot(3, 4, 4)
    
    strikes = np.linspace(80, 120, 21)
    implied_vols = []
    
    # Simulate market prices with volatility smile
    for strike in strikes:
        moneyness = strike / S0
        # Simulate typical volatility smile pattern
        if moneyness < 1:
            implied_vol = 0.2 + 0.05 * (1 - moneyness)**2  # Higher vol for ITM puts
        else:
            implied_vol = 0.2 + 0.03 * (moneyness - 1)**2  # Higher vol for OTM calls
        implied_vols.append(implied_vol)
    
    ax4.plot(strikes, np.array(implied_vols) * 100, 'mo-', linewidth=2, markersize=6)
    ax4.axvline(K, color='black', linestyle=':', alpha=0.7, label='ATM Strike')
    ax4.axvline(S0, color='green', linestyle=':', alpha=0.7, label='Current Price')
    
    ax4.set_title('Implied Volatility Smile')
    ax4.set_xlabel('Strike Price')
    ax4.set_ylabel('Implied Volatility (%)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 3D Surface: Option Price vs Stock Price and Volatility
    ax5 = plt.subplot(3, 4, 5, projection='3d')
    
    S_range = np.linspace(80, 120, 20)
    vol_range = np.linspace(0.1, 0.4, 20)
    S_grid, Vol_grid = np.meshgrid(S_range, vol_range)
    
    Price_grid = np.zeros_like(S_grid)
    for i in range(len(vol_range)):
        for j in range(len(S_range)):
            Price_grid[i, j] = model.black_scholes_call(S_grid[i, j], K, T, r, Vol_grid[i, j])
    
    surf = ax5.plot_surface(S_grid, Vol_grid * 100, Price_grid, cmap='viridis', alpha=0.8)
    ax5.set_title('Call Option Price Surface')
    ax5.set_xlabel('Stock Price')
    ax5.set_ylabel('Volatility (%)')
    ax5.set_zlabel('Option Price')
    
    # 6. Monte Carlo Convergence
    ax6 = plt.subplot(3, 4, 6)
    
    n_sims = [100, 500, 1000, 5000, 10000, 50000, 100000]
    mc_prices = []
    bs_price = model.black_scholes_call(S0, K, T, r, sigma)
    
    for n in n_sims:
        mc_price, _, _ = model.monte_carlo_option_price(S0, K, T, r, sigma, n)
        mc_prices.append(mc_price)
    
    ax6.semilogx(n_sims, mc_prices, 'bo-', label='Monte Carlo', linewidth=2)
    ax6.axhline(bs_price, color='red', linestyle='--', label='Black-Scholes', linewidth=2)
    
    ax6.set_title('Monte Carlo Convergence')
    ax6.set_xlabel('Number of Simulations')
    ax6.set_ylabel('Option Price')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 7. American vs European Option Values
    ax7 = plt.subplot(3, 4, 7)
    
    times_to_exp = np.linspace(0.01, 1, 20)
    american_prices = []
    european_prices = []
    
    for t in times_to_exp:
        american_price = model.binomial_tree_american(S0, K, t, r, sigma, 50, 'put')
        european_price = model.black_scholes_put(S0, K, t, r, sigma)
        
        american_prices.append(american_price)
        european_prices.append(european_price)
    
    ax7.plot(times_to_exp * 365, american_prices, 'b-', label='American Put', linewidth=2)
    ax7.plot(times_to_exp * 365, european_prices, 'r--', label='European Put', linewidth=2)
    
    ax7.set_title('American vs European Put Options')
    ax7.set_xlabel('Days to Expiration')
    ax7.set_ylabel('Option Price')
    ax7.legend()
    ax7.grid(True, alpha=0.3)
    
    # 8. Risk-Reversal Strategy
    ax8 = plt.subplot(3, 4, 8)
    
    # Risk reversal: Long call + Short put
    rr_payoffs = []
    for S in stock_prices:
        call_payoff = max(S - K, 0)
        put_payoff = max(K - S, 0)
        rr_payoff = call_payoff - put_payoff  # Long call, short put
        rr_payoffs.append(rr_payoff)
    
    # Add option premiums
    call_premium = model.black_scholes_call(S0, K, T, r, sigma)
    put_premium = model.black_scholes_put(S0, K, T, r, sigma)
    net_premium = call_premium - put_premium
    
    rr_pnl = np.array(rr_payoffs) - net_premium
    
    ax8.plot(stock_prices, rr_pnl, 'g-', linewidth=2, label='Risk Reversal P&L')
    ax8.axhline(0, color='black', linestyle='-', alpha=0.3)
    ax8.axvline(K, color='black', linestyle=':', alpha=0.7)
    ax8.fill_between(stock_prices, 0, rr_pnl, where=(rr_pnl > 0), alpha=0.3, color='green')
    ax8.fill_between(stock_prices, 0, rr_pnl, where=(rr_pnl <= 0), alpha=0.3, color='red')
    
    ax8.set_title('Risk Reversal Strategy P&L')
    ax8.set_xlabel('Stock Price at Expiration')
    ax8.set_ylabel('Profit/Loss')
    ax8.legend()
    ax8.grid(True, alpha=0.3)
    
    # 9. Volatility Term Structure
    ax9 = plt.subplot(3, 4, 9)
    
    maturities = [0.25, 0.5, 1, 2, 5]  # 3M, 6M, 1Y, 2Y, 5Y
    vol_term_structure = [0.18, 0.20, 0.22, 0.25, 0.28]  # Typical upward sloping
    
    # Calculate option prices for different maturities
    option_prices_by_maturity = []
    for T_mat, vol in zip(maturities, vol_term_structure):
        price = model.black_scholes_call(S0, K, T_mat, r, vol)
        option_prices_by_maturity.append(price)
    
    ax9_twin = ax9.twinx()
    
    line1 = ax9.plot(maturities, np.array(vol_term_structure) * 100, 'bo-', 
                     label='Implied Vol', linewidth=2)
    line2 = ax9_twin.plot(maturities, option_prices_by_maturity, 'ro-', 
                          label='Option Price', linewidth=2)
    
    ax9.set_title('Volatility Term Structure')
    ax9.set_xlabel('Time to Maturity (years)')
    ax9.set_ylabel('Implied Volatility (%)', color='blue')
    ax9_twin.set_ylabel('Option Price', color='red')
    
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax9.legend(lines, labels, loc='center left')
    ax9.grid(True, alpha=0.3)
    
    # 10. Greeks Heatmap
    ax10 = plt.subplot(3, 4, 10)
    
    # Create heatmap for different strikes and times
    strikes_heat = np.linspace(90, 110, 11)
    times_heat = np.linspace(0.05, 0.5, 10)
    
    delta_matrix = np.zeros((len(times_heat), len(strikes_heat)))
    
    for i, t in enumerate(times_heat):
        for j, k in enumerate(strikes_heat):
            greeks = model.calculate_greeks(S0, k, t, r, sigma, 'call')
            delta_matrix[i, j] = greeks['delta']
    
    im = ax10.imshow(delta_matrix, cmap='RdYlBu_r', aspect='auto', 
                     extent=[strikes_heat[0], strikes_heat[-1], 
                            times_heat[0] * 365, times_heat[-1] * 365])
    ax10.set_title('Delta Heatmap (Call Options)')
    ax10.set_xlabel('Strike Price')
    ax10.set_ylabel('Days to Expiration')
    plt.colorbar(im, ax=ax10, shrink=0.8)
    
    # 11. Portfolio Greeks
    ax11 = plt.subplot(3, 4, 11)
    
    # Create a sample portfolio
    portfolio = [
        {'type': 'call', 'strike': 95, 'quantity': 10},
        {'type': 'call', 'strike': 105, 'quantity': -5},
        {'type': 'put', 'strike': 95, 'quantity': -3},
        {'type': 'put', 'strike': 105, 'quantity': 8}
    ]
    
    portfolio_greeks = {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0, 'rho': 0}
    
    for position in portfolio:
        greeks = model.calculate_greeks(S0, position['strike'], T, r, sigma, position['type'])
        for greek in portfolio_greeks:
            portfolio_greeks[greek] += greeks[greek] * position['quantity']
    
    greek_names = list(portfolio_greeks.keys())
    greek_values = list(portfolio_greeks.values())
    colors = ['blue', 'green', 'red', 'orange', 'purple']
    
    bars = ax11.bar(greek_names, greek_values, color=colors, alpha=0.7)
    ax11.set_title('Portfolio Greeks')
    ax11.set_ylabel('Greek Value')
    ax11.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars, greek_values):
        height = bar.get_height()
        ax11.text(bar.get_x() + bar.get_width()/2., height + (0.01 if height >= 0 else -0.05),
                 f'{value:.2f}', ha='center', va='bottom' if height >= 0 else 'top')
    
    # 12. Stress Testing
    ax12 = plt.subplot(3, 4, 12)
    
    # Stress test scenarios
    scenarios = {
        'Base Case': {'S_mult': 1.0, 'vol_mult': 1.0},
        'Market Crash': {'S_mult': 0.8, 'vol_mult': 1.5},
        'Bull Market': {'S_mult': 1.2, 'vol_mult': 0.8},
        'High Vol': {'S_mult': 1.0, 'vol_mult': 2.0},
        'Low Vol': {'S_mult': 1.0, 'vol_mult': 0.5}
    }
    
    scenario_names = []
    portfolio_values = []
    
    for scenario_name, scenario in scenarios.items():
        scenario_names.append(scenario_name)
        
        # Calculate portfolio value under scenario
        portfolio_value = 0
        S_scenario = S0 * scenario['S_mult']
        vol_scenario = sigma * scenario['vol_mult']
        
        for position in portfolio:
            if position['type'] == 'call':
                option_price = model.black_scholes_call(S_scenario, position['strike'], 
                                                       T, r, vol_scenario)
            else:
                option_price = model.black_scholes_put(S_scenario, position['strike'], 
                                                      T, r, vol_scenario)
            
            portfolio_value += option_price * position['quantity']
        
        portfolio_values.append(portfolio_value)
    
    colors = ['green', 'red', 'blue', 'orange', 'purple']
    bars = ax12.bar(scenario_names, portfolio_values, color=colors, alpha=0.7)
    ax12.set_title('Portfolio Stress Testing')
    ax12.set_ylabel('Portfolio Value')
    ax12.tick_params(axis='x', rotation=45)
    
    # Add value labels
    for bar, value in zip(bars, portfolio_values):
        height = bar.get_height()
        ax12.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -5),
                 f'{value:.0f}', ha='center', va='bottom' if height >= 0 else 'top')
    
    plt.tight_layout()
    plt.show()
    
    # Print analysis summary
    print("\n=== Option Pricing and Greeks Analysis Summary ===")
    print(f"Base Parameters: S=${S0}, K=${K}, T={T:.2f} years, r={r:.1%}, σ={sigma:.1%}")
    
    bs_call = model.black_scholes_call(S0, K, T, r, sigma)
    bs_put = model.black_scholes_put(S0, K, T, r, sigma)
    print(f"\nBlack-Scholes Prices:")
    print(f"  Call: ${bs_call:.2f}")
    print(f"  Put: ${bs_put:.2f}")
    
    greeks_call = model.calculate_greeks(S0, K, T, r, sigma, 'call')
    print(f"\nCall Option Greeks:")
    for greek, value in greeks_call.items():
        print(f"  {greek.capitalize()}: {value:.4f}")
    
    mc_price, _, _ = model.monte_carlo_option_price(S0, K, T, r, sigma, 100000)
    print(f"\nMonte Carlo Price: ${mc_price:.2f} (vs BS: ${bs_call:.2f})")
    
    american_put = model.binomial_tree_american(S0, K, T, r, sigma, 100, 'put')
    european_put = model.black_scholes_put(S0, K, T, r, sigma)
    print(f"\nPut Option Comparison:")
    print(f"  American: ${american_put:.2f}")
    print(f"  European: ${european_put:.2f}")
    print(f"  Early Exercise Premium: ${american_put - european_put:.2f}")

if __name__ == "__main__":
    comprehensive_option_analysis() 