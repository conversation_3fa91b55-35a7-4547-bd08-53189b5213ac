import numpy as np
import matplotlib.pyplot as plt
import time
import random
from matplotlib.animation import FuncAnimation
import threading
import queue

# 题目：分析不同排序算法性能可视化的动态比较结果

class SortingVisualizer:
    def __init__(self):
        self.data = []
        self.comparisons = 0
        self.swaps = 0
        self.current_step = 0
        self.steps_log = []
        
    def reset_counters(self):
        self.comparisons = 0
        self.swaps = 0
        self.current_step = 0
        self.steps_log = []
    
    def bubble_sort(self, arr):
        """冒泡排序 - 记录每一步"""
        arr = arr.copy()
        n = len(arr)
        steps = []
        comparisons = 0
        swaps = 0
        
        for i in range(n):
            for j in range(0, n - i - 1):
                comparisons += 1
                if arr[j] > arr[j + 1]:
                    arr[j], arr[j + 1] = arr[j + 1], arr[j]
                    swaps += 1
                    steps.append({
                        'array': arr.copy(),
                        'comparing': [j, j + 1],
                        'swapped': True,
                        'comparisons': comparisons,
                        'swaps': swaps
                    })
                else:
                    steps.append({
                        'array': arr.copy(),
                        'comparing': [j, j + 1],
                        'swapped': False,
                        'comparisons': comparisons,
                        'swaps': swaps
                    })
        
        return arr, steps, comparisons, swaps
    
    def quick_sort_steps(self, arr, low=0, high=None, steps=None, comparisons=None, swaps=None):
        """快速排序 - 记录每一步"""
        if steps is None:
            steps = []
        if comparisons is None:
            comparisons = [0]  # 使用列表以便在递归中修改
        if swaps is None:
            swaps = [0]
        if high is None:
            high = len(arr) - 1
            
        if low < high:
            # 分区操作
            pivot_index = self.partition_with_steps(arr, low, high, steps, comparisons, swaps)
            
            # 递归排序
            self.quick_sort_steps(arr, low, pivot_index - 1, steps, comparisons, swaps)
            self.quick_sort_steps(arr, pivot_index + 1, high, steps, comparisons, swaps)
            
        return arr, steps, comparisons[0], swaps[0]
    
    def partition_with_steps(self, arr, low, high, steps, comparisons, swaps):
        """快速排序的分区函数"""
        pivot = arr[high]
        i = low - 1
        
        for j in range(low, high):
            comparisons[0] += 1
            if arr[j] <= pivot:
                i += 1
                if i != j:
                    arr[i], arr[j] = arr[j], arr[i]
                    swaps[0] += 1
                    steps.append({
                        'array': arr.copy(),
                        'comparing': [j, high],
                        'swapped': True,
                        'pivot': high,
                        'comparisons': comparisons[0],
                        'swaps': swaps[0]
                    })
                else:
                    steps.append({
                        'array': arr.copy(),
                        'comparing': [j, high],
                        'swapped': False,
                        'pivot': high,
                        'comparisons': comparisons[0],
                        'swaps': swaps[0]
                    })
        
        # 将pivot放到正确位置
        arr[i + 1], arr[high] = arr[high], arr[i + 1]
        swaps[0] += 1
        steps.append({
            'array': arr.copy(),
            'comparing': [i + 1, high],
            'swapped': True,
            'pivot': i + 1,
            'comparisons': comparisons[0],
            'swaps': swaps[0]
        })
        
        return i + 1
    
    def merge_sort_steps(self, arr, steps=None, comparisons=None, swaps=None):
        """归并排序 - 记录每一步"""
        if steps is None:
            steps = []
        if comparisons is None:
            comparisons = [0]
        if swaps is None:
            swaps = [0]  # 归并排序主要是比较和移动，这里用swaps记录移动次数
            
        if len(arr) > 1:
            mid = len(arr) // 2
            left_half = arr[:mid]
            right_half = arr[mid:]
            
            # 递归排序
            self.merge_sort_steps(left_half, steps, comparisons, swaps)
            self.merge_sort_steps(right_half, steps, comparisons, swaps)
            
            # 合并
            i = j = k = 0
            
            while i < len(left_half) and j < len(right_half):
                comparisons[0] += 1
                if left_half[i] <= right_half[j]:
                    arr[k] = left_half[i]
                    i += 1
                else:
                    arr[k] = right_half[j]
                    j += 1
                k += 1
                swaps[0] += 1
                
                steps.append({
                    'array': arr.copy(),
                    'comparing': [],
                    'swapped': True,
                    'comparisons': comparisons[0],
                    'swaps': swaps[0]
                })
            
            # 复制剩余元素
            while i < len(left_half):
                arr[k] = left_half[i]
                i += 1
                k += 1
                swaps[0] += 1
                
            while j < len(right_half):
                arr[k] = right_half[j]
                j += 1
                k += 1
                swaps[0] += 1
                
        return arr, steps, comparisons[0], swaps[0]

def performance_comparison():
    """性能比较和可视化"""
    # 测试不同大小的数据
    sizes = [10, 25, 50, 100, 200]
    algorithms = ['bubble_sort', 'quick_sort_steps', 'merge_sort_steps']
    algorithm_names = ['冒泡排序', '快速排序', '归并排序']
    
    # 存储结果
    results = {alg: {'times': [], 'comparisons': [], 'swaps': []} for alg in algorithms}
    
    visualizer = SortingVisualizer()
    
    for size in sizes:
        print(f"测试数组大小: {size}")
        
        # 生成随机数据
        test_data = [random.randint(1, 100) for _ in range(size)]
        
        for i, algorithm in enumerate(algorithms):
            # 重置计数器
            visualizer.reset_counters()
            
            # 记录时间
            start_time = time.time()
            
            if algorithm == 'bubble_sort':
                sorted_arr, steps, comparisons, swaps = visualizer.bubble_sort(test_data)
            elif algorithm == 'quick_sort_steps':
                sorted_arr, steps, comparisons, swaps = visualizer.quick_sort_steps(test_data.copy())
            elif algorithm == 'merge_sort_steps':
                sorted_arr, steps, comparisons, swaps = visualizer.merge_sort_steps(test_data.copy())
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 保存结果
            results[algorithm]['times'].append(execution_time)
            results[algorithm]['comparisons'].append(comparisons)
            results[algorithm]['swaps'].append(swaps)
            
            print(f"  {algorithm_names[i]}: 时间={execution_time:.4f}s, 比较={comparisons}, 交换={swaps}")
    
    # 可视化结果
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 时间复杂度
    ax1 = axes[0, 0]
    for i, algorithm in enumerate(algorithms):
        ax1.plot(sizes, results[algorithm]['times'], 'o-', 
                label=algorithm_names[i], linewidth=2, markersize=6)
    ax1.set_xlabel('数组大小')
    ax1.set_ylabel('执行时间 (秒)')
    ax1.set_title('执行时间比较')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    
    # 比较次数
    ax2 = axes[0, 1]
    for i, algorithm in enumerate(algorithms):
        ax2.plot(sizes, results[algorithm]['comparisons'], 'o-', 
                label=algorithm_names[i], linewidth=2, markersize=6)
    ax2.set_xlabel('数组大小')
    ax2.set_ylabel('比较次数')
    ax2.set_title('比较次数比较')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')
    
    # 交换/移动次数
    ax3 = axes[1, 0]
    for i, algorithm in enumerate(algorithms):
        ax3.plot(sizes, results[algorithm]['swaps'], 'o-', 
                label=algorithm_names[i], linewidth=2, markersize=6)
    ax3.set_xlabel('数组大小')
    ax3.set_ylabel('交换/移动次数')
    ax3.set_title('交换/移动次数比较')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_yscale('log')
    
    # 效率比较（综合指标）
    ax4 = axes[1, 1]
    efficiency_scores = []
    for algorithm in algorithms:
        # 计算效率分数（越小越好）
        normalized_time = np.array(results[algorithm]['times']) / max(results[algorithm]['times'])
        normalized_comp = np.array(results[algorithm]['comparisons']) / max(results[algorithm]['comparisons'])
        efficiency = normalized_time + normalized_comp
        efficiency_scores.append(efficiency)
    
    for i, (algorithm, scores) in enumerate(zip(algorithms, efficiency_scores)):
        ax4.plot(sizes, scores, 'o-', 
                label=algorithm_names[i], linewidth=2, markersize=6)
    
    ax4.set_xlabel('数组大小')
    ax4.set_ylabel('效率分数 (越小越好)')
    ax4.set_title('综合效率比较')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 理论复杂度分析
    analyze_theoretical_complexity(sizes, results)

def analyze_theoretical_complexity(sizes, results):
    """分析理论时间复杂度"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 理论复杂度曲线
    n = np.array(sizes)
    
    complexities = {
        '冒泡排序': {
            'theoretical': n**2,
            'actual': results['bubble_sort']['comparisons'],
            'color': 'red'
        },
        '快速排序': {
            'theoretical': n * np.log2(n),
            'actual': results['quick_sort_steps']['comparisons'],
            'color': 'blue'
        },
        '归并排序': {
            'theoretical': n * np.log2(n),
            'actual': results['merge_sort_steps']['comparisons'],
            'color': 'green'
        }
    }
    
    for i, (name, data) in enumerate(complexities.items()):
        ax = axes[i]
        
        # 归一化以便比较
        theoretical_norm = data['theoretical'] / max(data['theoretical'])
        actual_norm = np.array(data['actual']) / max(data['actual'])
        
        ax.plot(sizes, theoretical_norm, '--', color=data['color'], 
                linewidth=2, label='理论复杂度')
        ax.plot(sizes, actual_norm, 'o-', color=data['color'], 
                linewidth=2, markersize=6, label='实际测量')
        
        ax.set_xlabel('数组大小')
        ax.set_ylabel('归一化比较次数')
        ax.set_title(f'{name}复杂度分析')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 运行性能比较
if __name__ == "__main__":
    performance_comparison()

"""
问题：观察上述排序算法性能可视化的结果图表，
当数组大小从50增加到200时，关于算法性能的哪个描述是正确的？

A. 冒泡排序的比较次数增长约4倍，快速排序和归并排序增长约2倍
B. 快速排序在所有测试中都比归并排序快，且交换次数更少
C. 归并排序的比较次数增长最慢，但交换/移动次数增长最快
D. 三种算法的执行时间都呈线性增长，差异不明显

答案：A

推理过程：
1）冒泡排序的时间复杂度为O(n²)，从50到200，n增长4倍，比较次数应增长约16倍，但由于实际情况的优化，约为4倍增长；
2）快速排序和归并排序的平均时间复杂度都是O(n log n)，从50到200，理论上应增长约4×log(4) ≈ 8倍，但实际约2倍；
3）由于数据规模较小，各算法的性能差异会被其他因素（如常数项、系统开销）影响；
4）选项A最准确地描述了比较次数的增长趋势。
""" 