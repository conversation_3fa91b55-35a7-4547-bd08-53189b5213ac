import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from matplotlib.animation import FuncAnimation
import heapq
import time
from collections import defaultdict, deque
import random

class DynamicNetworkAnalyzer:
    def __init__(self, num_nodes=50, edge_probability=0.15):
        self.num_nodes = num_nodes
        self.edge_probability = edge_probability
        self.graphs = []
        self.create_dynamic_network()
    
    def create_dynamic_network(self):
        """Create a series of dynamic networks with changing topology"""
        np.random.seed(42)
        random.seed(42)
        
        # Create 10 time steps
        for t in range(10):
            G = nx.erdos_renyi_graph(self.num_nodes, self.edge_probability)
            
            # Add random weights and occasional failures
            for u, v in G.edges():
                # Base weight with time-varying component
                base_weight = np.random.uniform(1, 10)
                time_factor = 1 + 0.3 * np.sin(2 * np.pi * t / 10)
                G[u][v]['weight'] = base_weight * time_factor
                
                # Random edge failures (20% chance)
                if np.random.random() < 0.2:
                    G[u][v]['weight'] *= 5  # Simulate congestion
            
            # Add some new edges dynamically
            if t > 0:
                for _ in range(5):
                    u, v = np.random.choice(self.num_nodes, 2, replace=False)
                    if not G.has_edge(u, v):
                        G.add_edge(u, v, weight=np.random.uniform(1, 15))
            
            self.graphs.append(G)
    
    def dijkstra_performance(self, source, target):
        """Dijkstra algorithm with performance tracking"""
        results = []
        for G in self.graphs:
            start_time = time.perf_counter()
            try:
                path_length = nx.dijkstra_path_length(G, source, target)
                path = nx.dijkstra_path(G, source, target)
                computation_time = time.perf_counter() - start_time
                results.append({
                    'length': path_length,
                    'path': path,
                    'time': computation_time,
                    'nodes_visited': len(path)
                })
            except nx.NetworkXNoPath:
                results.append({
                    'length': float('inf'),
                    'path': [],
                    'time': time.perf_counter() - start_time,
                    'nodes_visited': 0
                })
        return results
    
    def astar_performance(self, source, target):
        """A* algorithm with performance tracking"""
        def heuristic(u, v):
            # Simple Euclidean distance heuristic
            pos = nx.spring_layout(G, seed=42)
            if u in pos and v in pos:
                return np.sqrt((pos[u][0] - pos[v][0])**2 + (pos[u][1] - pos[v][1])**2)
            return 0
        
        results = []
        for G in self.graphs:
            start_time = time.perf_counter()
            try:
                path_length = nx.astar_path_length(G, source, target, heuristic=heuristic)
                path = nx.astar_path(G, source, target, heuristic=heuristic)
                computation_time = time.perf_counter() - start_time
                results.append({
                    'length': path_length,
                    'path': path,
                    'time': computation_time,
                    'nodes_visited': len(path)
                })
            except nx.NetworkXNoPath:
                results.append({
                    'length': float('inf'),
                    'path': [],
                    'time': time.perf_counter() - start_time,
                    'nodes_visited': 0
                })
        return results
    
    def bellman_ford_performance(self, source, target):
        """Bellman-Ford algorithm with performance tracking"""
        results = []
        for G in self.graphs:
            start_time = time.perf_counter()
            try:
                path_length = nx.bellman_ford_path_length(G, source, target)
                path = nx.bellman_ford_path(G, source, target)
                computation_time = time.perf_counter() - start_time
                results.append({
                    'length': path_length,  
                    'path': path,
                    'time': computation_time,
                    'nodes_visited': len(path)
                })
            except nx.NetworkXNoPath:
                results.append({
                    'length': float('inf'),
                    'path': [],
                    'time': time.perf_counter() - start_time,
                    'nodes_visited': 0
                })
        return results

def analyze_and_visualize():
    """Main analysis and visualization function"""
    analyzer = DynamicNetworkAnalyzer()
    source, target = 0, 49
    
    # Run all algorithms
    dijkstra_results = analyzer.dijkstra_performance(source, target)
    astar_results = analyzer.astar_performance(source, target)
    bellman_results = analyzer.bellman_ford_performance(source, target)
    
    # Create comprehensive visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Dynamic Network Shortest Path Algorithm Performance Analysis', fontsize=16)
    
    # Time series data
    time_steps = range(10)
    
    # 1. Path lengths comparison
    ax1 = axes[0, 0]
    dijkstra_lengths = [r['length'] if r['length'] != float('inf') else None for r in dijkstra_results]
    astar_lengths = [r['length'] if r['length'] != float('inf') else None for r in astar_results]
    bellman_lengths = [r['length'] if r['length'] != float('inf') else None for r in bellman_results]
    
    ax1.plot(time_steps, dijkstra_lengths, 'bo-', label='Dijkstra', linewidth=2)
    ax1.plot(time_steps, astar_lengths, 'ro-', label='A*', linewidth=2)
    ax1.plot(time_steps, bellman_lengths, 'go-', label='Bellman-Ford', linewidth=2)
    ax1.set_title('Path Length Over Time')
    ax1.set_xlabel('Time Step')
    ax1.set_ylabel('Path Length')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Computation time comparison
    ax2 = axes[0, 1]
    dijkstra_times = [r['time'] * 1000 for r in dijkstra_results]  # Convert to ms
    astar_times = [r['time'] * 1000 for r in astar_results]
    bellman_times = [r['time'] * 1000 for r in bellman_results]
    
    ax2.plot(time_steps, dijkstra_times, 'bo-', label='Dijkstra', linewidth=2)
    ax2.plot(time_steps, astar_times, 'ro-', label='A*', linewidth=2)
    ax2.plot(time_steps, bellman_times, 'go-', label='Bellman-Ford', linewidth=2)
    ax2.set_title('Computation Time Over Time')
    ax2.set_xlabel('Time Step')
    ax2.set_ylabel('Time (ms)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')
    
    # 3. Network topology visualization at critical time points
    ax3 = axes[0, 2]
    # Show network at time step 5 (middle point)
    G = analyzer.graphs[5]
    pos = nx.spring_layout(G, seed=42)
    
    # Draw network
    nx.draw_networkx_edges(G, pos, alpha=0.3, width=0.5, ax=ax3)
    nx.draw_networkx_nodes(G, pos, node_color='lightblue', 
                          node_size=30, ax=ax3)
    
    # Highlight source and target
    nx.draw_networkx_nodes(G, pos, nodelist=[source], 
                          node_color='red', node_size=100, ax=ax3)
    nx.draw_networkx_nodes(G, pos, nodelist=[target], 
                          node_color='green', node_size=100, ax=ax3)
    
    # Draw shortest path if exists
    if dijkstra_results[5]['path']:
        path_edges = [(dijkstra_results[5]['path'][i], dijkstra_results[5]['path'][i+1]) 
                     for i in range(len(dijkstra_results[5]['path'])-1)]
        nx.draw_networkx_edges(G, pos, edgelist=path_edges, 
                              edge_color='red', width=2, ax=ax3)
    
    ax3.set_title('Network Topology at Time Step 5')
    ax3.axis('off')
    
    # 4. Algorithm efficiency heatmap
    ax4 = axes[1, 0]
    efficiency_data = np.array([
        dijkstra_times,
        astar_times, 
        bellman_times
    ])
    
    im = ax4.imshow(efficiency_data, cmap='RdYlBu_r', aspect='auto')
    ax4.set_title('Computation Time Heatmap')
    ax4.set_xlabel('Time Step')
    ax4.set_ylabel('Algorithm')
    ax4.set_yticks([0, 1, 2])
    ax4.set_yticklabels(['Dijkstra', 'A*', 'Bellman-Ford'])
    plt.colorbar(im, ax=ax4, label='Time (ms)')
    
    # 5. Path optimality analysis
    ax5 = axes[1, 1]
    # Calculate relative performance (compared to best path at each time)
    relative_performance = []
    for t in range(10):
        lengths = [dijkstra_lengths[t], astar_lengths[t], bellman_lengths[t]]
        valid_lengths = [l for l in lengths if l is not None]
        if valid_lengths:
            min_length = min(valid_lengths)
            rel_perf = [l/min_length if l is not None else np.nan for l in lengths]
            relative_performance.append(rel_perf)
        else:
            relative_performance.append([np.nan, np.nan, np.nan])
    
    rel_perf = np.array(relative_performance).T
    ax5.plot(time_steps, rel_perf[0], 'bo-', label='Dijkstra', linewidth=2)
    ax5.plot(time_steps, rel_perf[1], 'ro-', label='A*', linewidth=2)
    ax5.plot(time_steps, rel_perf[2], 'go-', label='Bellman-Ford', linewidth=2)
    ax5.axhline(y=1.0, color='black', linestyle='--', alpha=0.5)
    ax5.set_title('Path Optimality (Relative to Best)')
    ax5.set_xlabel('Time Step')
    ax5.set_ylabel('Relative Path Length')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. Stability analysis
    ax6 = axes[1, 2]
    # Calculate coefficient of variation for each algorithm
    dijkstra_cv = np.std([l for l in dijkstra_lengths if l is not None]) / np.mean([l for l in dijkstra_lengths if l is not None])
    astar_cv = np.std([l for l in astar_lengths if l is not None]) / np.mean([l for l in astar_lengths if l is not None])
    bellman_cv = np.std([l for l in bellman_lengths if l is not None]) / np.mean([l for l in bellman_lengths if l is not None])
    
    algorithms = ['Dijkstra', 'A*', 'Bellman-Ford']
    cvs = [dijkstra_cv, astar_cv, bellman_cv]
    colors = ['blue', 'red', 'green']
    
    bars = ax6.bar(algorithms, cvs, color=colors, alpha=0.7)
    ax6.set_title('Algorithm Stability\n(Coefficient of Variation)')
    ax6.set_ylabel('CV (σ/μ)')
    ax6.grid(True, alpha=0.3, axis='y')
    
    # Add value labels on bars
    for bar, cv in zip(bars, cvs):
        height = bar.get_height()
        ax6.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{cv:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
    # Print detailed analysis
    print("\n=== Dynamic Network Shortest Path Algorithm Analysis ===")
    print(f"Network: {analyzer.num_nodes} nodes, ~{len(analyzer.graphs[0].edges())} edges per time step")
    print(f"Source: {source}, Target: {target}")
    print(f"\nAlgorithm Performance Summary:")
    
    valid_dijkstra = [l for l in dijkstra_lengths if l is not None] 
    valid_astar = [l for l in astar_lengths if l is not None]
    valid_bellman = [l for l in bellman_lengths if l is not None]
    
    print(f"Dijkstra - Mean path length: {np.mean(valid_dijkstra):.2f}, Mean time: {np.mean(dijkstra_times):.4f}ms")
    print(f"A* - Mean path length: {np.mean(valid_astar):.2f}, Mean time: {np.mean(astar_times):.4f}ms")
    print(f"Bellman-Ford - Mean path length: {np.mean(valid_bellman):.2f}, Mean time: {np.mean(bellman_times):.4f}ms")

if __name__ == "__main__":
    analyze_and_visualize() 