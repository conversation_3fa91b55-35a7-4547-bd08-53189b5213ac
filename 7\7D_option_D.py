import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def create_loss_landscape():
    # 创建参数网格
    w1 = np.linspace(-3, 3, 150)
    w2 = np.linspace(-3, 3, 150)
    W1, W2 = np.meshgrid(w1, w2)
    
    # 选项D：马鞍点特征，两个沿轴分布的局部最小值
    loss = (
        0.1 * (W1**2 - W2**2) +  # 马鞍点基础项
        0.25 * (W1**4 + W2**4) +  # 稳定项
        0.3 * np.cos(1.8*W1) +  # W1方向振荡
        0.3 * np.cos(1.8*W2) +  # W2方向振荡
        0.6 * np.exp(-((W1-2.2)**2 + W2**2)/0.4) * (-1.1) +  # 右侧最小值
        0.6 * np.exp(-(W1**2 + (W2-2.2)**2)/0.4) * (-1.1) +  # 上方最小值
        0.02 * W1 * W2  # 轻微交叉项
    )
    
    return W1, W2, loss

def plot_loss_landscape():
    W1, W2, loss = create_loss_landscape()
    
    fig = plt.figure(figsize=(10, 8))
    
    # 等高线图
    ax1 = fig.add_subplot(1, 2, 1)
    contour = ax1.contour(W1, W2, loss, levels=25, colors='black', alpha=0.6, linewidths=0.8)
    contourf = ax1.contourf(W1, W2, loss, levels=25, cmap='RdYlBu_r', alpha=0.8)
    ax1.set_xlabel('权重 W1', fontsize=12)
    ax1.set_ylabel('权重 W2', fontsize=12)
    ax1.set_title('损失函数等高线图', fontsize=14)
    ax1.grid(True, alpha=0.3)
    plt.colorbar(contourf, ax=ax1)
    
    # 3D曲面图
    ax2 = fig.add_subplot(1, 2, 2, projection='3d')
    surf = ax2.plot_surface(W1[::3, ::3], W2[::3, ::3], loss[::3, ::3], 
                           cmap='viridis', alpha=0.8, linewidth=0, antialiased=True)
    ax2.set_xlabel('权重 W1', fontsize=12)
    ax2.set_ylabel('权重 W2', fontsize=12)
    ax2.set_zlabel('损失值', fontsize=12)
    ax2.set_title('3D损失函数曲面', fontsize=14)
    ax2.view_init(elev=30, azim=45)
    
    plt.tight_layout()
    plt.show()
    return fig

# 运行可视化
if __name__ == "__main__":
    plot_loss_landscape() 