用HTML、CSS和JavaScript复现图中宇宙引力模拟器的所有功能，要求天体运动符合万有引力定律，最终所有内容放在一个html里

我需要先查看当前的HTML文件来了解具体的实现细节，然后为你生成一个合适的prompt。

基于这个宇宙引力小游戏的代码，我为你生成一个适合给LLM出题的prompt：

---

**回答:**

1）图中核心功能：左键拖拽平移视角，滚轮缩放，右键拖拽放置天体并设定初速度，天体间根据万有引力定律相互作用，显示运动轨迹拖尾，碰撞时可选择合并质量和动量；

2）界面布局：左侧控制面板，有两个开关和四个按钮：暂停/继续、重置视角、添加示例系统、清空，底部显示天体数量和FPS，右侧还需要帮助面板说明操作方法；

3）实现方法：引力计算，并使用软化参数避免奇点，天体半径基于质量立方根
- 示例系统：一个恒星配三个轨道行星

4）样式要求：使用CSS变量定义配色方案，实现深色太空。面板使用毛玻璃效果和圆角设计
