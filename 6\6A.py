import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from scipy.fft import fft, fftfreq


def create_complex_wave():
    t = np.linspace(0, 4*np.pi, 1000)
    
    wave1 = np.sin(t) * np.exp(-0.1*t) 
    wave2 = 0.5 * np.sin(3*t) * np.exp(-0.05*t)
    wave3 = 0.25 * np.sin(5*t) * np.exp(-0.02*t)
    
    composite_wave = wave1 + wave2 + wave3
    
    # 计算傅里叶变换
    fft_vals = fft(composite_wave)
    freqs = fftfreq(len(t), t[1] - t[0])
    
    return t, composite_wave, fft_vals, freqs

def animate_wave_analysis(frame):
    plt.clf()
    
    t, wave, fft_vals, freqs = create_complex_wave()
    
    # 动态截取数据（模拟实时采集）
    end_idx = min(len(t), frame * 10 + 100)
    t_dynamic = t[:end_idx]
    wave_dynamic = wave[:end_idx]
    
    # 上方子图：时域信号
    plt.subplot(2, 1, 1)
    plt.plot(t_dynamic, wave_dynamic, 'b-', linewidth=2)
    plt.title(f'复合衰减波形分析 (样本点: {end_idx})')
    plt.ylabel('幅度')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 4*np.pi)
    plt.ylim(-2, 2)
    
    # 下方子图：频域分析
    plt.subplot(2, 1, 2)
    if end_idx > 50:  # 确保有足够的数据点进行FFT
        fft_dynamic = fft(wave_dynamic)
        freqs_dynamic = fftfreq(len(wave_dynamic), t[1] - t[0])
        
        # 只显示正频率部分
        pos_freqs = freqs_dynamic[:len(freqs_dynamic)//2]
        pos_fft = np.abs(fft_dynamic[:len(fft_dynamic)//2])
        
        plt.stem(pos_freqs, pos_fft, basefmt=' ')
        plt.title('频谱分析')
        plt.xlabel('频率 (Hz)')
        plt.ylabel('幅度')
        plt.xlim(0, 2)
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()

# 创建动画
fig = plt.figure(figsize=(12, 8))
anim = FuncAnimation(fig, animate_wave_analysis, frames=100, interval=100, repeat=True)

# 保存为gif（注释掉以避免实际执行时的问题）
# anim.save('wave_analysis.gif', writer='pillow', fps=10)

plt.show()

"""
问题：观看上述代码生成的动画，当动画播放到最后阶段（frame > 80）时，
下方频谱图中最主要的频率成分是什么？

A. 在频率0.16 Hz附近有最大峰值
B. 在频率0.80 Hz附近有最大峰值  
C. 在频率1.59 Hz附近有最大峰值
D. 频谱趋于平坦，无明显峰值

答案：B

推理过程：
1）代码中定义了三个衰减的正弦波：基波sin(t)、三次谐波sin(3t)、五次谐波sin(5t)；
2）由于衰减系数不同（0.1、0.05、0.02），五次谐波衰减最慢，在动画后期占主导地位；
3）五次谐波sin(5t)的频率为5/(2π) ≈ 0.796 Hz，在选项中最接近0.80 Hz；
4）因此当frame > 80时，频谱图中0.80 Hz附近应该有最大峰值。
""" 