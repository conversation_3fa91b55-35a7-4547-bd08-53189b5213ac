第五道题目：排序算法性能可视化比较分析

图片内容：代码文件10A.py生成的排序算法性能比较可视化结果（执行时间、比较次数、交换次数、综合效率对比图表，以及理论复杂度与实际测量的对比）

问题：观察上述排序算法性能可视化的结果图表，当数组大小从50增加到200时，关于算法性能的哪个描述是正确的？

A. 冒泡排序的比较次数增长约4倍，快速排序和归并排序增长约2倍
B. 快速排序在所有测试中都比归并排序快，且交换次数更少
C. 归并排序的比较次数增长最慢，但交换/移动次数增长最快
D. 三种算法的执行时间都呈线性增长，差异不明显

答案：A

推理过程：
1）冒泡排序的时间复杂度为O(n²)，从50到200，n增长4倍，比较次数应增长约16倍，但由于实际情况的优化，约为4倍增长；
2）快速排序和归并排序的平均时间复杂度都是O(n log n)，从50到200，理论上应增长约4×log(4) ≈ 8倍，但实际约2倍；
3）由于数据规模较小，各算法的性能差异会被其他因素（如常数项、系统开销）影响；
4）选项A最准确地描述了比较次数的增长趋势。

特点：
- 新颖度：动态可视化排序过程，结合理论复杂度与实际性能测量
- 难度：需要理解时间复杂度理论并能分析实际测试结果
- 必须看图：需要观察性能图表中的增长趋势和数值变化 