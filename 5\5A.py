import asyncio

async def alpha():
    print("Starting coroutine 'alpha'...")
    await asyncio.sleep(0.1)
    print("Executing alpha's main logic...")
    print("Coroutine 'alpha' finished.")

async def beta():
    print("Starting coroutine 'beta'...")
    await asyncio.sleep(0.1)
    print("Executing beta's main logic...")
    print("Coroutine 'beta' finished.")

async def main():
    await asyncio.gather(alpha(), beta())

asyncio.run(main())