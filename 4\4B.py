import random

def get_neighbors(x, y, width, height):
    """选项B：错误的宽度边界检查"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= nx < width - 1 and 0 <= ny < height:  # 错误的宽度边界
            neighbors.append((nx, ny))
    return neighbors

def generate_maze(width, height):
    """使用递归回溯算法生成迷宫"""
    # 初始化迷宫，全部为墙
    maze = [['#' for _ in range(width)] for _ in range(height)]
    
    # 访问标记
    visited = [[False for _ in range(width)] for _ in range(height)]
    
    def carve_path(x, y):
        """递归回溯算法核心函数"""
        # 标记当前位置为已访问并开辟路径
        visited[y][x] = True
        maze[y][x] = ' '
        
        # 获取邻居并随机打乱顺序
        neighbors = get_neighbors(x, y, width, height)
        random.shuffle(neighbors)
        
        for nx, ny in neighbors:
            if not visited[ny][nx]:
                # 开辟从当前位置到邻居的路径
                wall_x = x + (nx - x) // 2
                wall_y = y + (ny - y) // 2
                maze[wall_y][wall_x] = ' '
                
                # 递归处理邻居
                carve_path(nx, ny)
    
    # 从起点开始生成迷宫（确保起点坐标为奇数）
    start_x = 1 if width > 1 else 0
    start_y = 1 if height > 1 else 0
    carve_path(start_x, start_y)
    
    return maze

def print_maze(maze):
    """打印迷宫"""
    for row in maze:
        print(''.join(row))

def main():
    """主函数"""
    print("选项B：错误的宽度边界检查")
    print("=" * 50)
    
    # 生成迷宫
    width, height = 21, 15
    random.seed(42)  # 固定种子以便复现
    maze = generate_maze(width, height)
    
    print_maze(maze)
    print("\n分析：这个实现使用了错误的宽度边界检查 (width - 1)，")
    print("会导致右侧区域无法被访问，可能产生孤岛现象。")

if __name__ == "__main__":
    main() 