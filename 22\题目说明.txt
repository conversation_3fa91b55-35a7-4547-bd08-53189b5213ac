=== 题目22：多模态注意力机制代码调试与可视化分析 ===

**题目类型：** 客观选择题（代码调试+图像分析结合）

**图片描述：**
左侧显示一个多模态注意力机制的Python代码实现，其中包含关键错误：
```python
class BuggyMultiModalAttention(nn.Module):
    def forward(self, text_features, image_features):
        # 计算查询、键、值矩阵
        text_q = self.text_query(text_features)
        text_k = self.text_key(text_features)

        # ❌ 错误：缺少缩放因子
        text_scores = torch.matmul(text_q, text_k.transpose(-2, -1))
        cross_scores = torch.matmul(text_q, image_k.transpose(-2, -1))

        # 直接应用softmax，导致数值不稳定
        attention_weights = F.softmax(text_scores, dim=-1)
```

右侧显示该代码运行后的异常注意力热力图，其中：
- 最大值: 1.523 (>1.0 异常!)
- 最小值: -0.187 (<0 异常!)
- 热力图中可见明显的数值异常区域

**prompt（问题）：**
图中显示的多模态注意力机制代码存在数值稳定性问题，导致注意力权重出现负值和超过1的异常情况。请分析代码错误并选择正确的修复方案：

A. 在softmax函数前添加temperature scaling: attention_weights = F.softmax(scores / temperature, dim=-1)
B. 将注意力计算改为: scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_k)，然后应用softmax
C. 在多头拼接后添加LayerNorm: output = self.layer_norm(torch.cat(multi_head_outputs, dim=-1))
D. 将残差连接改为: output = self.dropout(attention_output) + residual

**answer（答案）：**
B

**推理过程：**
推理过程：1）观察图中代码发现注意力分数计算缺少缩放因子，直接使用torch.matmul(query, key.transpose(-2, -1))会导致分数过大；2）右侧热力图显示注意力权重出现负值和大于1的异常值，这违反了概率分布的基本性质；3）标准的scaled dot-product attention需要除以√d_k进行缩放，防止softmax输入过大导致梯度消失或爆炸；4）选项B正确实现了缩放机制，是Transformer架构中的标准做法，能从根本上解决数值稳定性问题。

**涉及知识点：**
- 多模态深度学习
- Scaled Dot-Product Attention机制
- 数值稳定性与梯度问题
- PyTorch张量操作
- 代码调试与错误分析

**难度特点：**
1. 需要同时理解代码逻辑和可视化结果的对应关系
2. 涉及深度学习中的数值稳定性核心问题
3. 要求对注意力机制的数学原理有深入理解
4. 结合了理论知识与实际调试技能
5. 需要识别标准Transformer实现中的关键细节
