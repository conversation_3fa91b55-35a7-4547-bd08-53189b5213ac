import tkinter as tk
from tkinter import Label, Button

root = tk.Tk()
root.title("Tkinter")
root.geometry("300x150")

label = Label(root, text="User Profile", bg="lightgrey")
label.grid(row=0, column=0, columnspan=2, sticky='ew', ipady=10)
save_btn = Button(root, text="Save")
save_btn.grid(row=1, column=0, sticky='w', padx=5, pady=5)
cancel_btn = But<PERSON>(root, text="Cancel")
cancel_btn.grid(row=1, column=1, sticky='w', padx=5, pady=5)


root.grid_columnconfigure(0, weight=0)
root.grid_columnconfigure(1, weight=1)


root.mainloop() 