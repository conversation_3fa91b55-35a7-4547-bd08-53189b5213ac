import random

def get_neighbors_A(x, y, width, height):
    """选项A：正确的边界检查"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= nx < width and 0 <= ny < height:  # 正确的边界检查
            neighbors.append((nx, ny))
    return neighbors

def get_neighbors_B(x, y, width, height):
    """选项B：错误的宽度边界检查"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= nx < width - 1 and 0 <= ny < height:  # 错误的宽度边界
            neighbors.append((nx, ny))
    return neighbors

def get_neighbors_C(x, y, width, height):
    """选项C：错误的高度边界检查"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= ny < height - 1 and 0 <= nx < width:  # 错误的高度边界
            neighbors.append((nx, ny))
    return neighbors

def get_neighbors_D(x, y, width, height):
    """选项D：错误的步长"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx, y + dy  # 错误的步长
        if 0 <= nx < width and 0 <= ny < height:
            neighbors.append((nx, ny))
    return neighbors

def generate_maze(width, height, get_neighbors_func):
    """使用指定的get_neighbors函数生成迷宫"""
    maze = [['#' for _ in range(width)] for _ in range(height)]
    visited = [[False for _ in range(width)] for _ in range(height)]
    
    def carve_path(x, y):
        visited[y][x] = True
        maze[y][x] = ' '
        
        neighbors = get_neighbors_func(x, y, width, height)
        random.shuffle(neighbors)
        
        for nx, ny in neighbors:
            if nx < width and ny < height and not visited[ny][nx]:
                wall_x = x + (nx - x) // 2
                wall_y = y + (ny - y) // 2
                if 0 <= wall_x < width and 0 <= wall_y < height:
                    maze[wall_y][wall_x] = ' '
                carve_path(nx, ny)
    
    start_x = 1 if width > 1 else 0
    start_y = 1 if height > 1 else 0
    carve_path(start_x, start_y)
    
    return maze

def print_maze(maze, title):
    """打印迷宫"""
    print(f"\n{title}")
    print("=" * 50)
    for row in maze:
        print(''.join(row))

def analyze_maze(maze, title):
    """分析迷宫特征"""
    width, height = len(maze[0]), len(maze)
    total_cells = width * height
    path_cells = sum(row.count(' ') for row in maze)
    wall_cells = total_cells - path_cells
    
    # 检查右下角是否有孤岛
    bottom_right_region = []
    start_x, start_y = max(0, width - 6), max(0, height - 6)
    for y in range(start_y, height):
        for x in range(start_x, width):
            bottom_right_region.append(maze[y][x])
    
    isolated_spaces = 0
    for y in range(1, height-1):
        for x in range(1, width-1):
            if maze[y][x] == ' ':
                # 检查是否被墙完全包围
                surrounded = True
                for dy in [-1, 0, 1]:
                    for dx in [-1, 0, 1]:
                        if dy == 0 and dx == 0:
                            continue
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < height and 0 <= nx < width:
                            if maze[ny][nx] == ' ':
                                surrounded = False
                                break
                    if not surrounded:
                        break
                if surrounded:
                    isolated_spaces += 1
    
    print(f"\n{title} - 分析结果:")
    print(f"总单元格: {total_cells}, 路径单元格: {path_cells}, 墙单元格: {wall_cells}")
    print(f"路径比例: {path_cells/total_cells:.2%}")
    print(f"可能的孤立空间: {isolated_spaces}")

def main():
    """主函数 - 对比测试"""
    width, height = 25, 17
    random.seed(123)  # 固定种子
    
    functions = [
        (get_neighbors_A, "选项A：正确实现"),
        (get_neighbors_B, "选项B：错误宽度边界 (width-1)"),
        (get_neighbors_C, "选项C：错误高度边界 (height-1)"),
        (get_neighbors_D, "选项D：错误步长 (dx,dy)")
    ]
    
    for func, title in functions:
        random.seed(123)  # 每次重置种子确保公平对比
        maze = generate_maze(width, height, func)
        print_maze(maze, title)
        analyze_maze(maze, title)
        print()

if __name__ == "__main__":
    main() 