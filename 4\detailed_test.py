import random

def get_neighbors_A(x, y, width, height):
    """选项A：正确的边界检查"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= nx < width and 0 <= ny < height:
            neighbors.append((nx, ny))
    return neighbors

def get_neighbors_B(x, y, width, height):
    """选项B：错误的宽度边界检查 - 会导致右侧孤岛"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= nx < width - 1 and 0 <= ny < height:  # 排除了最右侧的列
            neighbors.append((nx, ny))
    return neighbors

def get_neighbors_C(x, y, width, height):
    """选项C：错误的高度边界检查 - 会导致底部孤岛"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx*2, y + dy*2
        if 0 <= ny < height - 1 and 0 <= nx < width:  # 排除了最底部的行
            neighbors.append((nx, ny))
    return neighbors

def get_neighbors_D(x, y, width, height):
    """选项D：错误的步长 - 会生成密集网格"""
    neighbors = []
    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
        nx, ny = x + dx, y + dy  # 错误：步长为1而不是2
        if 0 <= nx < width and 0 <= ny < height:
            neighbors.append((nx, ny))
    return neighbors

def generate_maze_detailed(width, height, get_neighbors_func, title):
    """生成迷宫并显示详细过程"""
    maze = [['#' for _ in range(width)] for _ in range(height)]
    visited = [[False for _ in range(width)] for _ in range(height)]
    
    def carve_path(x, y):
        visited[y][x] = True
        maze[y][x] = ' '
        
        neighbors = get_neighbors_func(x, y, width, height)
        random.shuffle(neighbors)
        
        for nx, ny in neighbors:
            if 0 <= nx < width and 0 <= ny < height and not visited[ny][nx]:
                # 开辟中间的墙
                wall_x = x + (nx - x) // 2
                wall_y = y + (ny - y) // 2
                if 0 <= wall_x < width and 0 <= wall_y < height:
                    maze[wall_y][wall_x] = ' '
                carve_path(nx, ny)
    
    # 从左上角开始（奇数坐标）
    start_x = 1 if width > 1 else 0
    start_y = 1 if height > 1 else 0
    carve_path(start_x, start_y)
    
    return maze

def check_connectivity(maze):
    """检查迷宫的连通性"""
    width, height = len(maze[0]), len(maze)
    visited = [[False for _ in range(width)] for _ in range(height)]
    
    def dfs(x, y):
        if x < 0 or x >= width or y < 0 or y >= height or visited[y][x] or maze[y][x] == '#':
            return 0
        visited[y][x] = True
        count = 1
        for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
            count += dfs(x + dx, y + dy)
        return count
    
    components = []
    for y in range(height):
        for x in range(width):
            if maze[y][x] == ' ' and not visited[y][x]:
                size = dfs(x, y)
                if size > 0:
                    components.append((x, y, size))
    
    return components

def test_specific_case():
    """测试特定情况下的差异"""
    print("=== 详细测试：展示各选项的具体差异 ===\n")
    
    # 使用特定的迷宫参数来展示问题
    width, height = 11, 9  # 较小的迷宫便于观察
    
    tests = [
        (get_neighbors_A, "选项A：正确实现", 42),
        (get_neighbors_B, "选项B：错误宽度边界", 42),
        (get_neighbors_C, "选项C：错误高度边界", 42),
        (get_neighbors_D, "选项D：错误步长", 42)
    ]
    
    for func, title, seed in tests:
        print(f"\n{title}")
        print("=" * 60)
        random.seed(seed)
        maze = generate_maze_detailed(width, height, func, title)
        
        # 打印迷宫
        for i, row in enumerate(maze):
            print(f"{i:2d}: {''.join(row)}")
        
        # 分析连通性
        components = check_connectivity(maze)
        total_spaces = sum(row.count(' ') for row in maze)
        
        print(f"\n分析:")
        print(f"  总路径空间: {total_spaces}")
        print(f"  连通分量数: {len(components)}")
        
        if len(components) > 1:
            print(f"  警告：发现 {len(components)} 个分离的区域！")
            for i, (x, y, size) in enumerate(components):
                print(f"    分量 {i+1}: 起始于({x},{y})，大小={size}")
        else:
            print(f"  ✓ 迷宫是连通的")
        
        # 特别检查边界区域
        right_edge_spaces = sum(1 for y in range(height) if maze[y][width-1] == ' ')
        bottom_edge_spaces = sum(1 for x in range(width) if maze[height-1][x] == ' ')
        print(f"  右边缘路径数量: {right_edge_spaces}")
        print(f"  底边缘路径数量: {bottom_edge_spaces}")

def demonstrate_bug():
    """演示产生孤岛的具体场景"""
    print("\n=== 演示孤岛产生的场景 ===")
    
    # 创建一个手工设计的场景来展示问题
    width, height = 13, 9
    
    print(f"\n使用迷宫大小: {width}x{height}")
    print("在这个大小下，错误的边界检查会阻止算法访问边缘区域")
    
    for func, title in [(get_neighbors_B, "选项B"), (get_neighbors_C, "选项C")]:
        print(f"\n{title} - 边界检查分析:")
        
        # 分析在特定位置的邻居计算
        test_x, test_y = width - 4, height - 4  # 接近边界的位置
        
        print(f"  从位置 ({test_x}, {test_y}) 计算邻居:")
        neighbors = func(test_x, test_y, width, height)
        
        print(f"    正常应该检查的位置:")
        for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
            nx, ny = test_x + dx*2, test_y + dy*2
            valid_normal = 0 <= nx < width and 0 <= ny < height
            print(f"      ({nx}, {ny}) - 正常边界检查: {valid_normal}")
        
        print(f"    实际返回的邻居: {neighbors}")

if __name__ == "__main__":
    test_specific_case()
    demonstrate_bug() 