import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Rectangle
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns

# 设置随机种子确保结果可重现
np.random.seed(42)
plt.rcParams['font.family'] = 'SimHei'

def create_attention_visualization():
    """
    创建Vision Transformer (ViT)注意力机制可视化
    模拟对一张包含多个对象的图像进行patch分割和注意力计算
    """
    # 图像参数设置
    img_size = 224  # 标准ViT输入尺寸
    patch_size = 16  # patch大小
    num_patches = (img_size // patch_size) ** 2  # 14x14 = 196个patches
    
    # 创建模拟的图像patch特征
    # 模拟一张包含圆形、矩形和三角形的图像
    patches_grid = np.zeros((14, 14, 3))  # 14x14的patch网格，每个patch有RGB特征
    
    # 在特定位置放置不同的几何形状特征
    # 圆形区域 (左上角)
    circle_center = (3, 3)
    for i in range(14):
        for j in range(14):
            dist = np.sqrt((i - circle_center[0])**2 + (j - circle_center[1])**2)
            if dist <= 2.5:
                patches_grid[i, j] = [0.8, 0.2, 0.2]  # 红色圆形
    
    # 矩形区域 (右上角)
    rect_area = patches_grid[2:6, 8:12]
    rect_area[:, :] = [0.2, 0.8, 0.2]  # 绿色矩形
    
    # 三角形区域 (下方中央)
    triangle_center = (10, 7)
    for i in range(8, 13):
        for j in range(5, 10):
            if abs(j - triangle_center[1]) <= (12 - i):
                patches_grid[i, j] = [0.2, 0.2, 0.8]  # 蓝色三角形
    
    # 背景噪声
    for i in range(14):
        for j in range(14):
            if np.sum(patches_grid[i, j]) == 0:
                patches_grid[i, j] = np.random.normal(0.1, 0.05, 3)
    
    # 计算多头注意力权重
    num_heads = 8
    d_model = 768
    d_k = d_model // num_heads  # 96
    
    # 将patch特征转换为查询、键、值向量
    patches_flat = patches_grid.reshape(196, 3)
    
    # 模拟学习到的权重矩阵
    W_q = np.random.randn(3, d_k) * 0.1
    W_k = np.random.randn(3, d_k) * 0.1
    W_v = np.random.randn(3, d_k) * 0.1
    
    # 计算Q, K, V
    Q = patches_flat @ W_q
    K = patches_flat @ W_k
    V = patches_flat @ W_v
    
    # 计算注意力权重矩阵
    attention_scores = Q @ K.T / np.sqrt(d_k)
    attention_weights = np.exp(attention_scores) / np.sum(np.exp(attention_scores), axis=1, keepdims=True)
    
    # 为不同的头设计不同的注意力模式
    attention_heads = []
    
    for head in range(num_heads):
        # 每个头关注不同的特征模式
        head_attention = np.copy(attention_weights)
        
        if head == 0:  # 全局注意力头
            head_attention = np.ones((196, 196)) / 196
        elif head == 1:  # 局部邻域注意力头
            for i in range(14):
                for j in range(14):
                    patch_idx = i * 14 + j
                    for di in range(-1, 2):
                        for dj in range(-1, 2):
                            ni, nj = i + di, j + dj
                            if 0 <= ni < 14 and 0 <= nj < 14:
                                neighbor_idx = ni * 14 + nj
                                head_attention[patch_idx, neighbor_idx] *= 3
            # 重新归一化
            head_attention = head_attention / np.sum(head_attention, axis=1, keepdims=True)
        elif head == 2:  # 形状特异性注意力头 - 关注圆形
            circle_patches = []
            for i in range(14):
                for j in range(14):
                    if np.sum(patches_grid[i, j]) > 0.5 and patches_grid[i, j, 0] > 0.5:
                        circle_patches.append(i * 14 + j)
            
            for i in circle_patches:
                for j in circle_patches:
                    head_attention[i, j] *= 5
            head_attention = head_attention / np.sum(head_attention, axis=1, keepdims=True)
        elif head == 3:  # 形状特异性注意力头 - 关注矩形
            rect_patches = []
            for i in range(2, 6):
                for j in range(8, 12):
                    rect_patches.append(i * 14 + j)
            
            for i in rect_patches:
                for j in rect_patches:
                    head_attention[i, j] *= 5
            head_attention = head_attention / np.sum(head_attention, axis=1, keepdims=True)
        elif head == 4:  # 边缘检测注意力头
            for i in range(14):
                for j in range(14):
                    patch_idx = i * 14 + j
                    # 检测颜色变化大的区域
                    if i > 0 and j > 0 and i < 13 and j < 13:
                        color_diff = np.sum(np.abs(patches_grid[i, j] - patches_grid[i-1, j])) + \
                                   np.sum(np.abs(patches_grid[i, j] - patches_grid[i, j-1]))
                        if color_diff > 0.3:
                            # 增强边缘patch之间的注意力
                            for di in range(-2, 3):
                                for dj in range(-2, 3):
                                    ni, nj = i + di, j + dj
                                    if 0 <= ni < 14 and 0 <= nj < 14:
                                        neighbor_idx = ni * 14 + nj
                                        head_attention[patch_idx, neighbor_idx] *= 2
            head_attention = head_attention / np.sum(head_attention, axis=1, keepdims=True)
        else:
            # 其他头使用随机模式
            head_attention = np.random.exponential(0.1, (196, 196))
            head_attention = head_attention / np.sum(head_attention, axis=1, keepdims=True)
        
        attention_heads.append(head_attention)
    
    return patches_grid, attention_heads

def visualize_attention_analysis():
    """创建注意力分析可视化"""
    patches_grid, attention_heads = create_attention_visualization()
    
    # 创建4x2的子图布局
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    fig.suptitle('Vision Transformer 多头注意力机制分析\n(基于几何形状识别任务)', fontsize=16, fontweight='bold')
    
    # 显示原始patch网格
    axes[0, 0].imshow(patches_grid)
    axes[0, 0].set_title('输入图像Patch分割\n(圆形-红色, 矩形-绿色, 三角形-蓝色)', fontsize=12)
    axes[0, 0].set_xlabel('Patch列索引')
    axes[0, 0].set_ylabel('Patch行索引')
    
    # 添加网格线显示patch边界
    for i in range(15):
        axes[0, 0].axhline(i-0.5, color='white', linewidth=0.5, alpha=0.7)
        axes[0, 0].axvline(i-0.5, color='white', linewidth=0.5, alpha=0.7)
    
    # 显示不同注意力头的注意力权重热力图
    head_titles = [
        'Head 1: 全局注意力',
        'Head 2: 局部邻域注意力', 
        'Head 3: 圆形特异性注意力',
        'Head 4: 矩形特异性注意力',
        'Head 5: 边缘检测注意力',
        'Head 6: 随机模式注意力',
        'Head 7: 随机模式注意力'
    ]
    
    # 选择一个特定的查询patch (圆形区域中心)
    query_patch_idx = 3 * 14 + 3  # (3,3)位置的patch
    
    plot_positions = [(0,1), (0,2), (0,3), (1,0), (1,1), (1,2), (1,3)]
    
    for i, (row, col) in enumerate(plot_positions):
        if i < len(attention_heads):
            # 将注意力权重重塑为14x14的网格
            attention_map = attention_heads[i][query_patch_idx].reshape(14, 14)
            
            im = axes[row, col].imshow(attention_map, cmap='viridis', vmin=0, vmax=np.max(attention_map))
            axes[row, col].set_title(f'{head_titles[i]}\n查询位置: (3,3)', fontsize=10)
            axes[row, col].set_xlabel('Patch列索引')
            axes[row, col].set_ylabel('Patch行索引')
            
            # 标记查询patch位置
            axes[row, col].add_patch(Rectangle((2.5, 2.5), 1, 1, fill=False, edgecolor='red', linewidth=3))
            
            # 添加颜色条
            plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    plt.show()
    
    # 创建注意力权重统计分析图
    fig2, axes2 = plt.subplots(1, 3, figsize=(18, 6))
    fig2.suptitle('注意力权重统计分析', fontsize=16, fontweight='bold')
    
    # 分析1：不同头的注意力分布熵
    entropies = []
    head_names = ['全局', '局部', '圆形', '矩形', '边缘', '随机1', '随机2', '随机3']
    
    for i, attention_head in enumerate(attention_heads):
        # 计算每个查询位置的注意力分布熵
        head_entropies = []
        for query_idx in range(196):
            attention_dist = attention_head[query_idx]
            # 避免log(0)
            attention_dist = attention_dist + 1e-10
            entropy = -np.sum(attention_dist * np.log(attention_dist))
            head_entropies.append(entropy)
        entropies.append(np.mean(head_entropies))
    
    axes2[0].bar(range(len(entropies)), entropies, color=['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray'][:len(entropies)])
    axes2[0].set_title('各注意力头的平均熵值\n(熵值越高表示注意力越分散)', fontsize=12)
    axes2[0].set_xlabel('注意力头')
    axes2[0].set_ylabel('平均熵值')
    axes2[0].set_xticks(range(len(entropies)))
    axes2[0].set_xticklabels(head_names[:len(entropies)], rotation=45)
    
    # 分析2：形状特异性分析
    # 计算圆形区域内patch之间的平均注意力权重
    circle_patches = []
    for i in range(14):
        for j in range(14):
            dist = np.sqrt((i - 3)**2 + (j - 3)**2)
            if dist <= 2.5:
                circle_patches.append(i * 14 + j)
    
    rect_patches = []
    for i in range(2, 6):
        for j in range(8, 12):
            rect_patches.append(i * 14 + j)
    
    triangle_patches = []
    for i in range(8, 13):
        for j in range(5, 10):
            if abs(j - 7) <= (12 - i):
                triangle_patches.append(i * 14 + j)
    
    shape_attention_scores = []
    shapes = ['圆形内部', '矩形内部', '三角形内部']
    patch_groups = [circle_patches, rect_patches, triangle_patches]
    
    for head_idx in [2, 3, 4]:  # 圆形、矩形、边缘检测头
        head_scores = []
        for patches in patch_groups:
            if len(patches) > 1:
                # 计算该形状内部patch之间的平均注意力权重
                intra_attention = 0
                count = 0
                for i in patches:
                    for j in patches:
                        if i != j:
                            intra_attention += attention_heads[head_idx][i, j]
                            count += 1
                head_scores.append(intra_attention / count if count > 0 else 0)
            else:
                head_scores.append(0)
        shape_attention_scores.append(head_scores)
    
    x = np.arange(len(shapes))
    width = 0.25
    
    for i, (scores, label) in enumerate(zip(shape_attention_scores, ['圆形头', '矩形头', '边缘头'])):
        axes2[1].bar(x + i*width, scores, width, label=label)
    
    axes2[1].set_title('形状特异性注意力分析\n(各头对不同形状内部的注意力强度)', fontsize=12)
    axes2[1].set_xlabel('形状类型')
    axes2[1].set_ylabel('平均注意力权重')
    axes2[1].set_xticks(x + width)
    axes2[1].set_xticklabels(shapes)
    axes2[1].legend()
    
    # 分析3：注意力距离衰减分析
    distances = []
    attention_strengths = []
    
    # 选择圆形特异性头(Head 3)进行分析
    head_idx = 2
    query_patch = 3 * 14 + 3  # 圆形中心
    
    for i in range(14):
        for j in range(14):
            target_patch = i * 14 + j
            if target_patch != query_patch:
                # 计算空间距离
                query_i, query_j = 3, 3
                distance = np.sqrt((i - query_i)**2 + (j - query_j)**2)
                attention_strength = attention_heads[head_idx][query_patch, target_patch]
                
                distances.append(distance)
                attention_strengths.append(attention_strength)
    
    axes2[2].scatter(distances, attention_strengths, alpha=0.6, s=30)
    axes2[2].set_title('圆形特异性头的注意力距离衰减\n(查询位置: 圆形中心)', fontsize=12)
    axes2[2].set_xlabel('空间距离 (patch单位)')
    axes2[2].set_ylabel('注意力权重')
    
    # 添加趋势线
    z = np.polyfit(distances, attention_strengths, 2)
    p = np.poly1d(z)
    x_trend = np.linspace(min(distances), max(distances), 100)
    axes2[2].plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    visualize_attention_analysis()
