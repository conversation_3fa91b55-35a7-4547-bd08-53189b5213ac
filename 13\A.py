import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
import random

class DNASequenceAnalyzer:
    def __init__(self):
        self.nucleotides = ['A', 'T', 'G', 'C']
        
    def generate_sequence(self, length, gc_content=0.5):
        """Generate a random DNA sequence with specified GC content"""
        gc_count = int(length * gc_content)
        at_count = length - gc_count
        
        sequence = (['G', 'C'] * (gc_count // 2) + ['G'] * (gc_count % 2) +
                   ['A', 'T'] * (at_count // 2) + ['A'] * (at_count % 2))
        random.shuffle(sequence)
        return ''.join(sequence)
    
    def introduce_mutations(self, sequence, mutation_rate=0.1):
        """Introduce random mutations into a sequence"""
        mutated = list(sequence)
        for i in range(len(mutated)):
            if random.random() < mutation_rate:
                original = mutated[i]
                possible = [n for n in self.nucleotides if n != original]
                mutated[i] = random.choice(possible)
        return ''.join(mutated)

def create_nucleotide_composition_chart():
    """Create nucleotide composition analysis chart"""
    
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    analyzer = DNASequenceAnalyzer()
    
    sequences = {
        'Reference': 'ATGCGATCGAATTCGCTAGCATGCTAGCTAG',
        'Similar_90': analyzer.introduce_mutations('ATGCGATCGAATTCGCTAGCATGCTAGCTAG', 0.1),
        'Similar_70': analyzer.introduce_mutations('ATGCGATCGAATTCGCTAGCATGCTAGCTAG', 0.3),
        'GC_Rich': analyzer.generate_sequence(30, gc_content=0.65),
        'AT_Rich': analyzer.generate_sequence(30, gc_content=0.35),
        'Random': analyzer.generate_sequence(30, gc_content=0.5)
    }
    
    # Create visualization
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    seq_names = list(sequences.keys())
    compositions = []
    
    for seq_name, seq in sequences.items():
        comp = {nt: seq.count(nt) / len(seq) for nt in analyzer.nucleotides}
        compositions.append([comp[nt] for nt in analyzer.nucleotides])
    
    compositions = np.array(compositions)
    
    # Create stacked bar chart
    bottom = np.zeros(len(seq_names))
    colors = ['red', 'blue', 'green', 'orange']
    
    for i, nt in enumerate(analyzer.nucleotides):
        ax.bar(seq_names, compositions[:, i], bottom=bottom, 
               label=nt, color=colors[i], alpha=0.7)
        bottom += compositions[:, i]
    
    ax.set_title('Nucleotide Composition Analysis', fontsize=16, fontweight='bold')
    ax.set_ylabel('Frequency', fontsize=12)
    ax.legend(fontsize=12)
    ax.tick_params(axis='x', rotation=45, labelsize=10)
    ax.grid(True, alpha=0.3, axis='y')
    
    # Add percentage labels on bars
    for i, seq_name in enumerate(seq_names):
        cumulative = 0
        for j, nt in enumerate(analyzer.nucleotides):
            height = compositions[i, j]
            if height > 0.05:  # Only show label if segment is large enough
                ax.text(i, cumulative + height/2, f'{height:.1%}', 
                       ha='center', va='center', fontweight='bold', fontsize=9)
            cumulative += height
    
    plt.tight_layout()
    plt.show()
    
    # Print detailed composition data
    for seq_name, seq in sequences.items():
        comp = {nt: seq.count(nt) / len(seq) for nt in analyzer.nucleotides}
        gc_content = comp['G'] + comp['C']
        print(f"{seq_name}:")
        print(f"  A: {comp['A']:.1%}, T: {comp['T']:.1%}, G: {comp['G']:.1%}, C: {comp['C']:.1%}")
        print(f"  GC Content: {gc_content:.1%}")
        print(f"  Sequence: {seq}")
        print()

if __name__ == "__main__":
    create_nucleotide_composition_chart()

"""
选项A特征：
- 平衡的碱基组成，适度的GC含量变化
- Reference序列：标准的30bp序列
- Similar_90/70：通过突变产生的相似序列
- GC_Rich：65% GC含量
- AT_Rich：35% GC含量  
- Random：50% GC含量
"""
