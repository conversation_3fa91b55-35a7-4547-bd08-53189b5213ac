第十四道题目：期权价格模型希腊字母敏感性分析

图片内容：代码文件14A.py生成的期权定价模型综合分析可视化，包含期权价格与标的价格关系、希腊字母变化图、到期时间敏感性、隐含波动率微笑、3D价格曲面、蒙特卡洛收敛性、美式与欧式期权对比、风险逆转策略、波动率期限结构、希腊字母热力图、投资组合希腊字母和压力测试

问题：观察上述期权定价模型分析的可视化结果，在Delta和Gamma随标的价格变化的图中，当标的资产价格接近执行价格（At-The-Money, ATM）时，关于看涨期权的Gamma值特征，以下哪个结论最准确？

A. Gamma值在ATM处达到最小值，远离执行价格时逐渐增大
B. Gamma值在ATM处达到最大值，这表明Delta对标的价格变化最敏感
C. Gamma值在整个价格区间保持相对稳定，不受执行价格影响
D. Gamma值在ATM以下为负值，ATM以上为正值，在执行价格处为零

答案：B

推理过程：
1）Gamma是Delta对标的资产价格的一阶偏导数，衡量Delta变化的速度，即期权价格对标的资产价格变化的二阶敏感性；
2）在Black-Scholes模型中，Gamma = φ(d1)/(S×σ×√T)，其中φ(d1)是标准正态分布的概率密度函数；
3）当期权处于平价状态（ATM）时，d1接近于0，此时φ(d1)达到最大值（标准正态分布在0处的密度最大）；
4）因此Gamma在ATM处达到峰值，表明此时Delta对标的价格变化最为敏感，这也是为什么ATM期权的对冲成本最高的原因。

特点：
- 新颖度：结合量化金融、随机微积分、数值方法和金融衍生品定价理论
- 难度：需要理解Black-Scholes模型、希腊字母的金融含义、期权定价的数学原理和风险管理概念
- 必须看图：需要仔细观察Gamma曲线在不同标的价格下的形状变化，特别是在ATM附近的峰值特征 