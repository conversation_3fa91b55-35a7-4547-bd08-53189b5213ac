import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
import warnings


def differential_equation(y, t):
    """
    定义微分方程: dy/dt = -λy + sin(t)
    这是一个线性非齐次微分方程
    解析解: y(t) = (y0 + 1/(1+λ²))e^(-λt) + (sin(t) - λcos(t))/(1+λ²)
    """
    lambda_val = 10.0  # Stiffness parameter
    return -lambda_val * y + np.sin(t)

def analytical_solution(t, y0):
    """Analytical solution"""
    lambda_val = 10.0
    term1 = (y0 + 1/(1 + lambda_val**2)) * np.exp(-lambda_val * t)
    term2 = (np.sin(t) - lambda_val * np.cos(t)) / (1 + lambda_val**2)
    return term1 + term2

def euler_method(func, y0, t_span, h):
    """Explicit Euler method"""
    t = np.arange(t_span[0], t_span[1] + h, h)
    y = np.zeros(len(t))
    y[0] = y0

    for i in range(1, len(t)):
        y[i] = y[i-1] + h * func(y[i-1], t[i-1])

        # Check numerical instability
        if abs(y[i]) > 1e10:
            y[i:] = np.nan
            break

    return t, y

def implicit_euler(func, y0, t_span, h):
    """Implicit Euler method (simplified version for linear case)"""
    lambda_val = 10.0
    t = np.arange(t_span[0], t_span[1] + h, h)
    y = np.zeros(len(t))
    y[0] = y0

    for i in range(1, len(t)):
        # For dy/dt = -λy + sin(t), implicit Euler:
        # y[i] = y[i-1] + h*(-λ*y[i] + sin(t[i]))
        # y[i] = (y[i-1] + h*sin(t[i])) / (1 + h*λ)
        y[i] = (y[i-1] + h * np.sin(t[i])) / (1 + h * lambda_val)

    return t, y

def runge_kutta_4(func, y0, t_span, h):
    """Fourth-order Runge-Kutta method"""
    t = np.arange(t_span[0], t_span[1] + h, h)
    y = np.zeros(len(t))
    y[0] = y0

    for i in range(1, len(t)):
        k1 = h * func(y[i-1], t[i-1])
        k2 = h * func(y[i-1] + k1/2, t[i-1] + h/2)
        k3 = h * func(y[i-1] + k2/2, t[i-1] + h/2)
        k4 = h * func(y[i-1] + k3, t[i-1] + h)

        y[i] = y[i-1] + (k1 + 2*k2 + 2*k3 + k4) / 6

        # Check numerical instability
        if abs(y[i]) > 1e10:
            y[i:] = np.nan
            break

    return t, y

def analyze_numerical_methods():
    """Analyze stability of different numerical methods"""
    y0 = 1.0  # Initial condition
    t_span = [0, 2]  # Time range

    # Different step sizes
    step_sizes = [0.1, 0.15, 0.2, 0.25]

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()

    colors = ['blue', 'red', 'green', 'orange']
    methods = [
        ('Explicit Euler', euler_method),
        ('Implicit Euler', implicit_euler),
        ('4th-order R-K', runge_kutta_4),
        ('SciPy ODE', lambda func, y0, t_span, h:
         (np.linspace(t_span[0], t_span[1], int((t_span[1]-t_span[0])/h) + 1),
          odeint(func, y0, np.linspace(t_span[0], t_span[1], int((t_span[1]-t_span[0])/h) + 1)).flatten()))
    ]

    # Analytical solution
    t_analytical = np.linspace(t_span[0], t_span[1], 1000)
    y_analytical = analytical_solution(t_analytical, y0)

    for idx, (method_name, method_func) in enumerate(methods):
        ax = axes[idx]

        # Plot analytical solution
        ax.plot(t_analytical, y_analytical, 'k--', linewidth=2,
                label='Analytical', alpha=0.8)

        # Test different step sizes
        for i, h in enumerate(step_sizes):
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    t_num, y_num = method_func(differential_equation, y0, t_span, h)

                # Handle NaN values
                valid_mask = ~np.isnan(y_num)
                if np.sum(valid_mask) > 1:
                    ax.plot(t_num[valid_mask], y_num[valid_mask],
                           color=colors[i], marker='o', markersize=3,
                           label=f'h={h}', alpha=0.7)
                else:
                    # If all NaN, mark as unstable
                    ax.text(0.5, 0.8 - i*0.1, f'h={h}: Unstable',
                           transform=ax.transAxes, color=colors[i],
                           fontweight='bold')

            except Exception as e:
                ax.text(0.5, 0.8 - i*0.1, f'h={h}: Failed',
                       transform=ax.transAxes, color=colors[i])

        ax.set_xlabel('Time t')
        ax.set_ylabel('y(t)')
        ax.set_title(f'{method_name}方法')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(-2, 5)  # Limit y-axis range for observation

    plt.tight_layout()
    plt.show()

    # Stability analysis
    analyze_stability_regions()

def analyze_stability_regions():
    """Analyze stability regions of different methods"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # Create complex plane grid
    real = np.linspace(-4, 2, 300)
    imag = np.linspace(-3, 3, 300)
    R, I = np.meshgrid(real, imag)
    z = R + 1j * I

    methods_stability = [
        ('Explicit Euler', lambda z: np.abs(1 + z) <= 1),
        ('Implicit Euler', lambda z: np.abs(1 / (1 - z)) <= 1),
        ('4th-order R-K', lambda z: np.abs(1 + z + z**2/2 + z**3/6 + z**4/24) <= 1)
    ]

    for idx, (method_name, stability_func) in enumerate(methods_stability):
        ax = axes[idx]

        # Calculate stability region
        stable = stability_func(z)

        # Plot stability region
        ax.contourf(R, I, stable.astype(int), levels=[0.5, 1.5],
                   colors=['lightblue'], alpha=0.7)
        ax.contour(R, I, stable.astype(int), levels=[0.5],
                  colors=['blue'], linewidths=2)

        # Mark special points
        lambda_val = -10.0  # Eigenvalue of our differential equation
        step_sizes = [0.1, 0.15, 0.2, 0.25]

        for h in step_sizes:
            point = h * lambda_val
            color = 'red' if not stability_func(np.array([point]))[0] else 'green'
            ax.plot(point.real, point.imag, 'o', color=color, markersize=8,
                   label=f'hλ, h={h}')

        ax.set_xlabel('Real part Re(hλ)')
        ax.set_ylabel('Imaginary part Im(hλ)')
        ax.set_title(f'{method_name}稳定性区域')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_xlim(-4, 2)
        ax.set_ylim(-3, 3)

    plt.tight_layout()
    plt.show()

# Run analysis
if __name__ == "__main__":
    analyze_numerical_methods()

"""
问题：观察上述数值方法求解刚性微分方程的可视化结果，
当步长h=0.2时，哪种说法是正确的？

A. 显式欧拉方法保持稳定，四阶龙格-库塔方法出现振荡
B. 隐式欧拉方法最稳定，显式欧拉和四阶R-K方法都不稳定  
C. 所有方法都保持稳定，但隐式欧拉精度最高
D. 四阶龙格-库塔方法最精确，但显式欧拉方法最稳定

答案：B

推理过程：
1）微分方程dy/dt = -10y + sin(t)是刚性方程，特征值λ = -10；
2）对于h=0.2，hλ = -2，这个值落在显式欧拉稳定区域（|1+hλ|≤1，即|1-2|=1）的边界上，实际会不稳定；
3）四阶R-K方法的稳定区域虽然更大，但hλ=-2仍可能超出其稳定区域；
4）隐式欧拉方法具有无条件稳定性（A-稳定），对任何负实数hλ都稳定。
""" 